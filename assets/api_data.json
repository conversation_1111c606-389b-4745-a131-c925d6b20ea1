[{"collectionName": "App Upload APIs", "children": [{"folderName": "App Upload", "children": [{"folderName": "Added Folder", "children": [{"requestData": "Added Request", "method": "POST"}, {"requestData": "Added Request2", "method": "GET"}]}, {"requestData": "App Upload Log", "method": "POST"}, {"requestData": "App Upload Log Copy", "method": "GET"}, {"requestData": "App List to Upload", "method": "GET"}]}, {"folderName": "New App Create Request", "children": [{"requestData": "app create", "method": "POST"}, {"requestData": "update app", "method": "POST"}]}, {"folderName": "App Create - Bis", "children": [{"requestData": "Get Package Name", "method": "POST"}, {"requestData": "Get App Request List", "method": "GET"}, {"requestData": "New App Upload Request", "method": "POST"}, {"requestData": "App Upload Log By Filter", "method": "POST"}, {"requestData": "iOS App Sync", "method": "POST"}]}, {"requestData": "local Data Scrphhh", "method": "POST"}, {"requestData": "feed me onlinebbb", "method": "POST"}, {"requestData": "S3 File copy cmdhh", "method": "POST"}]}, {"collectionName": "Apps Version Update", "children": [{"requestData": "local Datahhh Scrp", "method": "POST"}, {"requestData": "feed mesmsm online", "method": "POST"}, {"requestData": "S3 Filenxnx copy cmd", "method": "POST"}]}, {"collectionName": "Product App", "children": [{"requestData": "localsns Data Scrp", "method": "POST"}, {"requestData": "feedjs me online", "method": "POST"}, {"requestData": "S3 File copy cmdsmdmd", "method": "POST"}]}]