class Globals{

  static String usrId = "123";

  static String iconType = fillColor;

  static String fill = "-fill";
  static String fillColor = "-fill-color";
  static String outlineColor = "-outline-color";
  static String outline = "-outline";

  static String MY_TIME_ZONE = 'Europe/London';
  static const THEME_STATUS = "THEMESTATUS";

  static List<String> countryCode = <String>["+91","+44"];
  static List<String> appearanceList = <String>["Default","Light Mode","Dark Mode"];
  static List<String> mapNavigationList = <String>["In App Map Navigation","Other"];


  //static size - (Text, Margin etc..)
  static const double headerText = 20.0;
  static const double formFieldText = 18.0;
  static const double titleHeaderText = 18.0;
  static const double titleText = 16.0;
  static const double bodyText = 14.0;
  static const double smallText = 12.0;
  static const double textInputErrorText = 12.0;
  static const double parentLeftPadding = 16.0;
  static const double parentRightPadding = 16.0;

  static const double outlineBorderRadius = 8.0;
  static const double defaultTextInputBorderWidth = 1.0;
  static double defaultTextInputCornerRadius = 4.0;
  static double defaultTextInputTextSize = 16.0;
  static double dropdownHeight = 40.0;
  static double textfieldTopBottompadding = 14.0;


  //Text input static variable
  static double horizontalGap = 10.0;
  static double verticalGap = 14.0;

  //for this project
  static double labelTextSizeForDialog = 14.0;





  //button height
  static const double buttonHeight = 60.0;

  //for container capsule
  static const double containerCapsuleCornerRadius = 8.0;

  //for image
  static const double iconPrefixSuffixHeightWidth = 16.0;

  //for tab
  static const double selectedTabTextSize = 17.0;
  static const double unSelectedTabTextSize = 15.0;
  static const double tabIndicatorHeight = 4.0;
  static const double tabIndicatorBackgroundHeight = 1.0;//4.0;

  //for card
  static const double cardElevation = 0.0;
  static const double outlineCardBorderRadius = 8.0;
  static const double outlineCardBorderWidth = 1.5;
  static double cardInnerPadding = 16.0;

  //for divider
  static double dividerHeight = 1;
  static double dividerMarginLeft = 8.0;
  static double dividerMarginRight = 8.0;

  //for order list
  static const double orderListHead = 18.0;
  static const double orderListTitle = 16.0;
  static const double orderListBody = 14.0;
  static const double orderListCommonText = 12.0;



  //for home
  static const double trackingTextSize = 15.0;
  static const double cancelTextSize = 17.0;
  static const double topHeaderTextSize = 16.0;
  static const double bodyTextSize = 16.0;
  static const double capsuleBoxHeaderTextSize = 14.0;
  static const double textSummarySize = 20.0;

  //for account
  static const double topContainerHeight = 120.0;
  static const double containerCircleImage = 60.0;
  static const double containerTopBarPadding = 16.0;
  static const double containerImageRadius = 6.0;
  static const double imageContainerBackgroundHeightWidth = 46.0;
  static const double accountHeader = 20.0;
  static const double accountTitle = 18.0;
  static const double accountBody = 16.0;
  static const double accountCommonText = 14.0;
  static const double accountSmallText = 12.0;

  static const double myLocationSize = 24.0;
  static const int textMaxLines = 2;

  //for store access
  static const double storeAccessHeader = 18.0;
  static const double storeAccessTitle = 16.0;
  static const double storeAccessBody = 14.0;
  static const double storeAccessSmallText = 12.0;

  //for Order Summery
  static const double orderSummeryHeader = 18.0;
  static const double orderSummeryTitle = 16.0;
  static const double orderSummeryBody = 14.0;
  static const double orderSummerySmallText = 12.0;

  static const double tabIconSize = 25.0;

  static const double buttonTextFontSize = 18.0;

  //for profile
  static const double profileImageCircle = 254.0;
  static const double profileTextHeader = 20.0;
  static const double profileTextTitle = 18.0;
  static const double profileTextBody = 14.0;

  //for Pagination
  static const int pagination_value = 20;


}