import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../globals.dart';
import '../../helpers/color_config.dart';
import '../../helpers/string_config.dart';
import '../../helpers/style_config.dart';
import 'CommonSVGIcon.dart';
import 'CommonText.dart';

  class CommonTextFormField extends StatefulWidget {
  final TextEditingController textEditController;
  EdgeInsetsGeometry padding;
  final bool autofocus;
  final String? hintTextString;
  final TextStyle? hintStyle;
  final String? helperTextString;
  final TextStyle? helperStyle;
  final bool isLabelText;
  final bool isLabelTextBold;
  final String? labelText;
  final int? minLength;
  final String passwordValidationType;
  final String numberValidationType;
  final bool recheck;
  final String? customErrorMessage;
  final InputType inputType;
  final BorderType borderType;
  final Color? themeColor;
  final double? cornerRadius;
  final int? maxLength;
  final Widget? prefixIcon;
  final Color? textColor;
  final String? errorMessage;
  final bool isValidationRequired;
  final bool isOptionalButNeedToValidate;
  final Function()? onTab;
  final Function()? onEditingComplete;
  final Function(String)? onChange;
  final Function(String)? onSubmit;
  final bool readOnly;
  bool visibility;
  final bool isTextCenter;
  final String counterTextValue;
  final FocusNode? focusnode;
  final Color? cursorColor;
  final double? fontSize;
  final Widget? prefix;
  final Widget? suffix;
  final EdgeInsetsGeometry? contentPadding;
  final bool? enabled;
  String? Function(String?)? validator;
  final Widget? suffixIcon;
  final String? suffixImageName;
  final double? suffixImageSize;
  final double? suffixImagePadding;
  final bool? needFixedHeight;
  final int? maxLines;
  final String? obscuringCharacter;
  final String? initialValue;
  final bool? showCounterText;
  final TextInputAction? textInputAction;
  final bool? defaultValidator;
  final bool visibilityOnOffSwitch;
  final bool isDense;
  double? labelTextSize;

  //prefix & suffix icon
  final bool isPrefix;
  final bool isSuffix;
  final String imageName;
  final String imagePath;
  final Color imageColor;
  final double imageHeight;
  final double imageWidth;
  final double? fieldHeight;

  CommonTextFormField({super.key,
    required this.textEditController,
    this.padding = const EdgeInsets.only(left: 0, top: 0, right: 0, bottom: 0),
    this.autofocus = false,
    this.hintTextString,
    this.hintStyle,
    this.helperTextString,
    this.helperStyle,
    this.isLabelText = true,
    this.isLabelTextBold = false,
    this.labelText,
    this.minLength,
    this.passwordValidationType = 'min',
    this.numberValidationType = 'max',
    this.recheck = false,
    this.customErrorMessage,
    this.inputType = InputType.Default,
    this.borderType = BorderType.Default,
    this.themeColor,
    this.cornerRadius,
    this.maxLength,
    this.prefixIcon,
    this.textColor,
    this.errorMessage,
    this.isValidationRequired = false,
    this.isOptionalButNeedToValidate = false,
    this.onTab,
    this.onEditingComplete,
    this.onChange,
    this.onSubmit,
    this.readOnly = false,
    this.visibility = false,
    this.isTextCenter = false,
    this.counterTextValue = "",
    this.focusnode,
    this.cursorColor ,
    this.fontSize=16,
    this.prefix,
    this.suffix,
    this.contentPadding,
    this.enabled,
    this.validator,
    this.suffixIcon,
    this.suffixImageName,
    this.suffixImageSize,
    this.suffixImagePadding,
    this.needFixedHeight,
    this.maxLines,
    this.obscuringCharacter,
    this.initialValue,
    this.showCounterText,
    this.textInputAction,
    this.defaultValidator=false,
    this.visibilityOnOffSwitch=false,
    this.isDense=false,
    this.labelTextSize = 18.0,
    this.imageName = '',
    this.imagePath = '',
    this.isSuffix = false,
    this.isPrefix = false,
    this.imageColor = AppThemeColor.textInputPrefixSuffix,
    this.imageHeight = Globals.iconPrefixSuffixHeightWidth,
    this.imageWidth = Globals.iconPrefixSuffixHeightWidth,
    this.fieldHeight = 40,

  });

  @override
  State<CommonTextFormField> createState() => _CommonTextFormFieldState();
}

  class _CommonTextFormFieldState extends State<CommonTextFormField> {
  String validationMessage = '';
  bool _isValidate = false;

  final double borderWidth = Globals.defaultTextInputBorderWidth;
  final double horizontalGap = Globals.horizontalGap;
  final double verticalGap = Globals.verticalGap;
  final double outlineBorderRadius = Globals.defaultTextInputCornerRadius;

  late Color defaultTextColor;
  late Color defaultCursorColor;
  late Color defaultActiveColor;
  late Color defaultDisableColor;
  late Color defaultFocusColor;
  late Color hintTextColor;
  final Color defaultErrorColor = AppThemeColor.red;

  @override
  Widget build(BuildContext context) {
    defaultTextColor = AppThemeColor.textColorBlackMidGrey(context);
    hintTextColor = AppThemeColor.textInputHintColorGrayMidGrey(context);
    defaultCursorColor = AppThemeColor.textColorRedGrey(context);
    defaultActiveColor = AppThemeColor.textFieldBorderActiveColor;
    defaultDisableColor = AppThemeColor.backgroundTextInputBox(context);
    defaultFocusColor = AppThemeColor.backgroundTextInputBox(context);

    return Padding(
        padding: widget.padding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if(widget.isLabelText == true)...[
              CommonText(
                text: widget.labelText ?? '',
                textStyle: widget.isLabelTextBold == true ? mBoldTextStyle16(textColor: defaultTextColor, textSize: widget.labelTextSize) : mMediumTextStyle16(textColor: defaultTextColor, textSize: widget.labelTextSize),
              ),
              const SizedBox(height: 8),
            ],
            TextFormField(
              controller: widget.textEditController,
              obscuringCharacter:widget.obscuringCharacter??'*',
              cursorColor: widget.cursorColor ?? defaultCursorColor,
              autofocus: widget.autofocus,
              onChanged: widget.onChange,
              enabled: widget.enabled,
              initialValue:widget.initialValue,
              validator:getValidator(),
              onTap: () {
                if(widget.readOnly){
                  widget.onTab?.call();
                }
              },
              onEditingComplete: widget.onEditingComplete,
              textAlign: widget.isTextCenter ? TextAlign.center : TextAlign.left,
              focusNode: widget.focusnode,
              decoration: textFieldDecoration(context),
              keyboardType: getKeyboardType(),
              obscureText: widget.visibility,
              maxLines:widget.maxLines??1,
              maxLength:widget.maxLength,
              style: getTextStyle(),
              readOnly: widget.readOnly,
              inputFormatters: getFormatter(),
              textInputAction: widget.textInputAction??TextInputAction.next,
            ),
          ],
        ));
  }

  InputDecoration textFieldDecoration(BuildContext context) {
    return InputDecoration(
      contentPadding: widget.contentPadding ?? EdgeInsets.symmetric(vertical: verticalGap, horizontal: horizontalGap),
      counterText :widget.showCounterText==true?null:widget.inputType == InputType.MultilineText && widget.maxLength!=0?null:"",
      border: getBorder(),
      enabledBorder: getBorder(),
      disabledBorder:disabledBorder() ,
      focusedBorder: getFocusBorder(),
      errorBorder: getErrorBorder(),
      focusedErrorBorder: getErrorBorder(),
      filled: false,
      isDense: widget.isDense,
      fillColor: Colors.transparent,
      prefix: widget.prefix,
      suffix: widget.suffix,
      prefixIcon: widget.prefixIcon,
      suffixIcon:widget.visibilityOnOffSwitch??false?getSuffixIcon(): widget.suffixIcon,
      hintText: widget.hintTextString,
      hintStyle: widget.hintStyle??getHintTextStyle(context),
      helperText: widget.helperTextString,
      helperStyle: widget.helperStyle,
    );
  }

  //get border of textinput filed
  getBorder() {
    if (widget.borderType == BorderType.Default) {
      return OutlineInputBorder(
          borderRadius: BorderRadius.circular(widget.cornerRadius ?? outlineBorderRadius),
          borderSide: BorderSide(width:borderWidth, color:widget.enabled??true ? AppThemeColor.commonBorderColor : AppThemeColor.commonBorderColor)
      );
    }
    else if (widget.borderType == BorderType.UnderLine) {
      return UnderlineInputBorder(
          borderRadius: BorderRadius.circular(widget.cornerRadius ?? outlineBorderRadius),
          borderSide: BorderSide(width:borderWidth, color:AppThemeColor.textInputActiveColor)
      );
    } else {
      return InputBorder.none;
    }
  }

  disabledBorder() {
    if (widget.borderType == BorderType.Default) {
      return OutlineInputBorder(
          borderRadius: BorderRadius.circular(widget.cornerRadius ?? outlineBorderRadius),
          borderSide: BorderSide(width:borderWidth, color:defaultDisableColor)
      );
    }
    else if (widget.borderType == BorderType.UnderLine) {
      return UnderlineInputBorder(
          borderRadius: BorderRadius.circular(widget.cornerRadius ?? outlineBorderRadius),
          borderSide: BorderSide(width:borderWidth, color:defaultDisableColor)
      );
    } else {
      return InputBorder.none;
    }
  }
  // get Focus of textinput filed
  getFocusBorder() {
    if (widget.borderType == BorderType.Default) {
      return OutlineInputBorder(
          borderRadius: BorderRadius.circular(widget.cornerRadius ?? outlineBorderRadius),
          borderSide: BorderSide(width:borderWidth, color:defaultFocusColor)
      );
    }
    else if (widget.borderType == BorderType.UnderLine) {
      return UnderlineInputBorder(
          borderRadius: BorderRadius.circular(widget.cornerRadius ?? outlineBorderRadius),
          borderSide: BorderSide(width:borderWidth, color:defaultFocusColor)
      );
    } else {
      return InputBorder.none;
    }
  }
  //get error border of textinput filed
  getErrorBorder() {
    if (widget.borderType == BorderType.Default) {
      return OutlineInputBorder(
          borderRadius: BorderRadius.circular(widget.cornerRadius ?? outlineBorderRadius),
          borderSide: BorderSide(width:borderWidth, color:defaultErrorColor)
      );
    }
    else if (widget.borderType == BorderType.UnderLine) {
      return UnderlineInputBorder(
          borderRadius: BorderRadius.circular(widget.cornerRadius ?? outlineBorderRadius),
          borderSide: BorderSide(width:borderWidth, color:defaultErrorColor)
      );
    } else {
      return InputBorder.none;
    }
  }
  //get focus error border of textinput filed
  getFocusErrorBorder() {
    if (widget.borderType == BorderType.Default) {
      return OutlineInputBorder(
          borderRadius: BorderRadius.circular(widget.cornerRadius ?? outlineBorderRadius),
          borderSide: BorderSide(width:borderWidth, color:defaultErrorColor)
      );
    }
    else if (widget.borderType == BorderType.UnderLine) {
      return UnderlineInputBorder(
          borderRadius: BorderRadius.circular(widget.cornerRadius ?? outlineBorderRadius),
          borderSide: BorderSide(width:borderWidth, color:defaultErrorColor)
      );
    } else {
      return InputBorder.none;
    }
  }
  // formatter on basis of textinput type
  List<TextInputFormatter> getFormatter() {
    if (widget.inputType == InputType.Number) {
      return [FilteringTextInputFormatter.digitsOnly];
    } else if (widget.inputType == InputType.Amount) {
      return [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))];
    } else if (widget.inputType == InputType.Percentage) {
      return [FilteringTextInputFormatter.allow(RegExp(r'^((?!0)\d{1,10}|0|\.\d{1,2})($|\.$|\.\d{1,2}$)'))];
    } else if (widget.inputType == InputType.PhoneNo) {
      return [FilteringTextInputFormatter.digitsOnly];
    }
    // else if (widget.inputType == InputType.MultilineText){
    //    return [LengthLimitingTextInputFormatter(5)];
    // }width: borderWidth
    else {
      return [TextInputFormatter.withFunction((oldValue, newValue) => newValue)];
    }
  }

  // text style for textinput
  TextStyle getTextStyle() {
    return mRegularTextStyle16(
      textColor: widget.enabled??true?defaultTextColor:defaultDisableColor,
      textSize: widget.fontSize,
    );
  }
  TextStyle getHintTextStyle(BuildContext context) {
    return mRegularTextStyle16(
      textColor: hintTextColor,
      textSize: widget.fontSize,
    );
  }
  // return input type for setting keyboard
  TextInputType getInputType() {
    switch (widget.inputType) {
      case InputType.Default:
        return TextInputType.text;
      case InputType.Email:
        return TextInputType.emailAddress;
      case InputType.BulkEmail:
        return TextInputType.text;
      case InputType.BulkNumber:
        return TextInputType.text;
      case InputType.Number:
        return TextInputType.number;
      case InputType.Amount:
        return TextInputType.numberWithOptions(signed: true, decimal: true);
      case InputType.Percentage:
        return TextInputType.numberWithOptions(signed: true, decimal: true);
      case InputType.MultilineText:
        return TextInputType.multiline;
      case InputType.PhoneNo:
        return TextInputType.number;
      case InputType.Pin:
        return TextInputType.number;
      default:
        return TextInputType.text;
    }
  }

  // get prefix Icon
  getPrefixIcon() {
    return CommonSVGIcon(imageName: widget.imageName, imagePath: widget.imagePath, color: widget.imageColor, height: widget.imageHeight, width: widget.imageWidth);
  }

  // get suffix icon
  getSuffixIcon() {
    //if (widget.inputType == InputType.Password || widget.inputType == InputType.Pin) {
    return IconButton(
        onPressed: () {
          widget.visibility = !widget.visibility;
          setState(() {});
        },
        icon: CommonSVGIcon(imageName: widget.imageName, imagePath: widget.imagePath, color: widget.imageColor, height: widget.imageHeight, width: widget.imageWidth,)
       );
  }

  TextInputType getKeyboardType() {
    switch (widget.inputType) {
      case InputType.Default:
        return TextInputType.multiline;
      case InputType.Email:
        return TextInputType.emailAddress;
      case InputType.Number:
        return TextInputType.number;
      case InputType.PhoneNo:
        return TextInputType.phone;
      case InputType.Amount:
        return const TextInputType.numberWithOptions(decimal: true);
      case InputType.Pin:
        return TextInputType.number;
      default:
        return TextInputType.multiline;
    }
  }

  String checkValidationStatus({String val = '', bool validationIsRequired = false, InputType? type, TextEditingController? textController, String? errorMessage}) {
    if (validationIsRequired == true) {
      if (type == InputType.Default) {
        _isValidate = val.isEmpty;
        if (_isValidate == true) {
          validationMessage = widget.errorMessage ?? 'Field cannot be empty';
        } else {
          validationMessage = '';
        }
      } else if (type == InputType.MultilineText) {
        _isValidate = val.isEmpty;
        if (_isValidate == true) {
          validationMessage = widget.errorMessage ?? 'Field cannot be empty';
        } else {
          validationMessage = '';
        }
      } else if (type == InputType.BulkEmail) {
        List<String> emails = val.split(",");

        for (String element in emails) {
          _isValidate = !RegExp(r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+").hasMatch(element.trim());

          if (element.isEmpty) {
            validationMessage = 'Please fill in this field';
            break;
          } else if (_isValidate == true) {
            validationMessage = widget.errorMessage ?? 'Please enter a valid email address';
            break;
          } else {
            validationMessage = '';
          }
        }
      } else if (type == InputType.Email) {
        _isValidate = !RegExp(r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+").hasMatch(val);

        if (val.isEmpty) {
          validationMessage = 'Please enter your email address'; /*widget.errorMessage ?? 'Please fill in this field';*/
        } else if (_isValidate == true) {
          validationMessage = widget.errorMessage ?? 'Please enter a valid email address';
        } else {
          validationMessage = '';
        }
      } else if (type == InputType.Number) {
        if (widget.numberValidationType == 'min') {
          _isValidate = (val.length < widget.minLength!.toInt());
        } else {
          _isValidate = !(val.length == widget.maxLength);
        }
        if (val.isEmpty) {
          validationMessage = widget.errorMessage ?? 'Please fill in this field';
        } else if (_isValidate == true) {
          validationMessage = widget.errorMessage ?? 'Contact Number is not valid';
        } else {
          validationMessage = '';
        }
      } else if (type == InputType.BulkNumber) {
        List<String> numbers = val.split(",");

        for (String element in numbers) {
          String number = element.trim();
          if (!number.isEmpty) {
            if (number.substring(0, 1) == '0') {
              number = number.substring(1);
            }
          }
          _isValidate = !(number.length == 10);
          if (number.isEmpty) {
            validationMessage = StringConfig.pleaseProvideYourTelephoneNumber;
            break;
          } else if (_isValidate == true) {
            validationMessage = widget.customErrorMessage ?? StringConfig.pleaseProvideYourTelephoneNumber;
            break;
          } else {
            validationMessage = '';
          }
        }
      } else if (type == InputType.PhoneNo) {
        if (!val.isEmpty) {
          //if(val.substring(0,1) == '0'&& val.length==11){
          if (val.substring(0, 1) == '0') {
            val = val.substring(1);
          }
        }
        //_isValidate = !(val.length > 9 && val.length < 12);
        bool _getIsValidate(val){
          bool returnValue=true;
          if(val.length == 10){
            returnValue=false;
          }else if(val.length == 11 && val[0]=='0'){
            returnValue=false;
          }
          return returnValue;
        }
        _isValidate = _getIsValidate(val);
        if (val.isEmpty) {
          validationMessage = widget.errorMessage ?? 'Please fill in this field';
        } else if (_isValidate == true) {
          validationMessage = widget.customErrorMessage ??  StringConfig.pleaseProvideYourTelephoneNumber;
        } else {
          validationMessage = '';
        }
      } else if (type == InputType.Amount) {
        if (widget.numberValidationType == 'min') {
          _isValidate = (val.length < widget.minLength!.toInt());
        } else {
          _isValidate = !(val.length == widget.maxLength);
        }
        if (val.isEmpty) {
          validationMessage = widget.errorMessage ?? 'Please fill in this field';
        } else if (_isValidate == true) {
          validationMessage = widget.errorMessage ?? 'Please fill in this field';
        } else {
          validationMessage = '';
        }
      } else if (type == InputType.Percentage) {
        if (widget.numberValidationType == 'min') {
          _isValidate = (val.length < widget.minLength!.toInt());
        } else {
          _isValidate = !(val.length == widget.maxLength);
        }
        if (val.isEmpty) {
          validationMessage = widget.errorMessage ?? 'Please fill in this field';
        } else if (_isValidate == true) {
          validationMessage = widget.errorMessage ?? 'Please fill in this field';
        } else {
          validationMessage = '';
        }
      } else if (widget.inputType == InputType.Password || widget.inputType == InputType.Pin) {
        //password validation
        //_isValidate = RegExp(r'^(?=.?[A-Z])(?=.?[a-z])(?=.?[0-9])(?=.?[!@#\$&*~]).{8,}$').hasMatch(textFieldValue);
        //validationMessage = widget.errorMessage ?? 'Password is not valid';
        if (widget.recheck == false) {
          if (widget.passwordValidationType == 'min') {
            _isValidate = (val.length < widget.minLength!.toInt());
          } else {
            _isValidate = !(val.length == widget.maxLength);
          }

          if (_isValidate == true) {
            if (val.isEmpty == true) {
              validationMessage = widget.errorMessage ?? 'Password is not valid';
            } else {
              validationMessage = widget.customErrorMessage ?? 'Password should be minimum 6 digit long';
            }
            //validationMessage = widget.errorMessage ?? 'Password is not valid';
          } else {
            validationMessage = '';
          }
        }
      }
    }
    return validationMessage;
  }

  getValidator() {
    if(widget.defaultValidator??false){
      return widget.validator;
    }else{
      if(widget.isValidationRequired){
        return (v){
          var text = checkValidationStatus(validationIsRequired: widget.isValidationRequired, type: widget.inputType, val: v!, textController: widget.textEditController, errorMessage: widget.errorMessage);
          if(text.isNotEmpty){
            setState(() {validationMessage = text;});
            return validationMessage;
          }
          else{
            setState(() {validationMessage = "";});
            return null;
          }
        };
      }else{
        if( widget.isOptionalButNeedToValidate){
          return (v){
            if (v!.isNotEmpty){
              var text = checkValidationStatus(validationIsRequired: widget.isOptionalButNeedToValidate, type: widget.inputType, val: v, textController: widget.textEditController, errorMessage: widget.errorMessage);
              if(text.isNotEmpty){
                setState(() {validationMessage = text;});
                return validationMessage;
              }
              else{
                setState(() {validationMessage = "";});
                return null;
              }
            }else{
              setState(() {validationMessage = ""; _isValidate = false;});
              return null;
            }
          };
        }else{
          return null;
        }
      }
    }
  }
}

class AlwaysDisabledFocusNode extends FocusNode {
  @override
  bool get hasFocus => false;
}

enum InputType { Default, Email, Number, Password, Amount, MultilineText, PhoneNo, BulkNumber, BulkEmail, Pin, Percentage }
//border Type
enum BorderType { Default, UnderLine, none }
