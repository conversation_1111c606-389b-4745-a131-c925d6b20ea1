import 'package:flutter/material.dart';

import '../../globals.dart';
import '../../helpers/color_config.dart';
import '../../helpers/style_config.dart';
import 'CommonSVGIcon.dart';
import 'CommonText.dart';




/*class CommonDropdown extends StatefulWidget {
  final String? initialText;
  final Icon? dropdownIcon;
  final double? height;
  final List<String> items;
  final Color backgroundColor;
  final Color dropdownBackgroundColor;
  final TextStyle? textStyle;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? textPadding;
  final EdgeInsetsGeometry? iconPadding;
  final ValueChanged<String>? onChanged;
  final bool isBorder;

  const CommonDropdown({
    Key? key,
    this.initialText,
    this.dropdownIcon,
    this.height,
    required this.items,
    this.backgroundColor = Colors.black,
    this.dropdownBackgroundColor = Colors.grey,
    this.textStyle,
    this.padding,
    this.margin,
    this.textPadding,
    this.iconPadding,
    this.onChanged,
    this.isBorder = false
  }) : super(key: key);

  @override
  _CommonDropdownState createState() => _CommonDropdownState();
}

class _CommonDropdownState extends State<CommonDropdown> {
  String? _selectedItem;

  @override
  void initState() {
    super.initState();
    _selectedItem = widget.initialText ?? widget.items.first;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: widget.padding ?? const EdgeInsets.all(0),
      margin: widget.margin ?? const EdgeInsets.all(0),
      height: widget.height,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(4),
        border: widget.isBorder
            ? Border.all(
          color: AppThemeColor.commonBorderColor,
          width: 1,
        )
            : null,
      ),
      child: Row(
        children: [
          Expanded(
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: _selectedItem,
                dropdownColor: widget.dropdownBackgroundColor,
                icon: Padding(
                  padding: widget.iconPadding ?? const EdgeInsets.all(0),
                  child: widget.dropdownIcon ??
                      const CommonSVGIcon(
                        imageName: 'direction_down',
                        imagePath: 'images',
                        color: AppThemeColor.commonDropdownArrowColor,
                        height: 24,
                        width: 24,
                        //padding: EdgeInsets.all(12.0),
                      ),
                      //const Icon(Icons.arrow_drop_down, color: Colors.white),
                ),
                isDense: false,
                isExpanded: false,
                style: widget.textStyle ??
                    const TextStyle(color: Colors.white, fontSize: 14),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedItem = value;
                    });
                    if (widget.onChanged != null) {
                      widget.onChanged!(value);
                    }
                  }
                },
                items: widget.items
                    .map((item) => DropdownMenuItem<String>(
                  value: item,
                  child: Padding(
                    padding: widget.textPadding ?? const EdgeInsets.all(0),
                    child: CommonText(
                      text: item,
                      textStyle: mRegularTextStyle16(
                          textSize: 14, textColor: AppThemeColor.white),
                    ),
                  ),
                ))
                    .toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}*/





//2nd one
/*class CommonDropdown extends StatefulWidget {
  final String? initialText;
  final Icon? dropdownIcon;
  final double? height;
  final List<String> items;
  final Color backgroundColor;
  final Color dropdownBackgroundColor;
  final TextStyle? textStyle;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? textPadding;
  final EdgeInsetsGeometry? iconPadding;
  final ValueChanged<String>? onChanged;
  final bool isBorder;

  const CommonDropdown({
    Key? key,
    this.initialText,
    this.dropdownIcon,
    this.height,
    required this.items,
    this.backgroundColor = Colors.black,
    this.dropdownBackgroundColor = Colors.grey,
    this.textStyle,
    this.padding,
    this.margin,
    this.textPadding,
    this.iconPadding,
    this.onChanged,
    this.isBorder = false,
  }) : super(key: key);

  @override
  _CommonDropdownState createState() => _CommonDropdownState();
}

class _CommonDropdownState extends State<CommonDropdown> {
  String? _selectedItem;
  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();
    _selectedItem = widget.initialText ?? widget.items.first;
  }

  void _showDropdown(BuildContext context) {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final Offset offset = renderBox.localToGlobal(Offset.zero);
    final Size size = renderBox.size;

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: offset.dx,
        top: offset.dy + size.height,
        width: size.width,
        child: Material(
          color: Colors.transparent,
          child: Container(
            decoration: BoxDecoration(
              color: widget.dropdownBackgroundColor,
              borderRadius: BorderRadius.circular(4),
              boxShadow: [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: 4,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: ListView.builder(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              itemCount: widget.items.length,
              itemBuilder: (context, index) {
                final item = widget.items[index];
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedItem = item;
                    });
                    _overlayEntry?.remove();
                    _overlayEntry = null;
                    if (widget.onChanged != null) {
                      widget.onChanged!(item);
                    }
                  },
                  child: Container(
                    height: widget.height ?? 40,
                    padding: widget.textPadding ??
                        const EdgeInsets.symmetric(horizontal: 16),
                    alignment: Alignment.centerLeft,
                    color: _selectedItem == item
                        ? Colors.grey[700]
                        : Colors.transparent,
                    child: Text(
                      item,
                      style: widget.textStyle ??
                          const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                          ),
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideDropdown() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  void dispose() {
    _hideDropdown();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (_overlayEntry == null) {
          _showDropdown(context);
        } else {
          _hideDropdown();
        }
      },
      child: Container(
        padding: widget.padding ?? const EdgeInsets.symmetric(horizontal: 12),
        margin: widget.margin ?? const EdgeInsets.all(0),
        height: widget.height,
        decoration: BoxDecoration(
          color: widget.backgroundColor,
          borderRadius: BorderRadius.circular(4),
          border: widget.isBorder
              ? Border.all(
            color: Colors.grey[700]!,
            width: 1,
          )
              : null,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(left: 0),
                child: CommonText(
                  text: _selectedItem ?? '',
                  textStyle: mRegularTextStyle16(
                      textSize: 14, textColor: AppThemeColor.white),
                ),
              ),
            ),
            Padding(
              padding: widget.iconPadding ?? const EdgeInsets.all(8.0),
              child: widget.dropdownIcon ??
                  const CommonSVGIcon(
                    imageName: 'direction_down',
                    imagePath: 'images',
                    color: AppThemeColor.commonDropdownArrowColor,
                    height: 24,
                    width: 24,
                    //padding: EdgeInsets.all(12.0),
                  ),
            ),
          ],
        ),
      ),
    );
  }
}*/





/*class CommonDropdown extends StatefulWidget {
  final String? initialText;
  final Icon? dropdownIcon;
  final double? height;
  final List<String> items;
  final Color backgroundColor;
  final Color dropdownBackgroundColor;
  final TextStyle? textStyle;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? textPadding;
  final EdgeInsetsGeometry? iconPadding;
  final ValueChanged<String>? onChanged;
  final bool isBorder;

  const CommonDropdown({
    Key? key,
    this.initialText,
    this.dropdownIcon,
    this.height,
    required this.items,
    this.backgroundColor = Colors.black,
    this.dropdownBackgroundColor = Colors.grey,
    this.textStyle,
    this.padding,
    this.margin,
    this.textPadding,
    this.iconPadding,
    this.onChanged,
    this.isBorder = false,
  }) : super(key: key);

  @override
  _CommonDropdownState createState() => _CommonDropdownState();
}

class _CommonDropdownState extends State<CommonDropdown> {
  String? _selectedItem;
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();

  @override
  void initState() {
    super.initState();
    _selectedItem = widget.initialText ?? widget.items.first;
  }

  void _showDropdown() {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final Size size = renderBox.size;
    final Offset offset = renderBox.localToGlobal(Offset.zero);

    _overlayEntry = OverlayEntry(
      builder: (context) => GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: _hideDropdown,
        child: Stack(
          children: [
            Positioned(
              left: offset.dx,
              top: offset.dy + size.height,
              width: size.width,
              child: Material(
                color: Colors.transparent,
                child: Container(
                  decoration: BoxDecoration(
                    color: widget.dropdownBackgroundColor,
                    borderRadius: BorderRadius.circular(4),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black26,
                        blurRadius: 4,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ListView.builder(
                    padding: EdgeInsets.zero,
                    shrinkWrap: true,
                    itemCount: widget.items.length,
                    itemBuilder: (context, index) {
                      final item = widget.items[index];
                      return MouseRegion(
                        onEnter: (_) => setState(() => _selectedItem = item),
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedItem = item;
                            });
                            _hideDropdown();
                            if (widget.onChanged != null) {
                              widget.onChanged!(item);
                            }
                          },
                          child: Container(
                            height: widget.height ?? 40,
                            padding: widget.textPadding ??
                                const EdgeInsets.symmetric(horizontal: 16),
                            alignment: Alignment.centerLeft,
                            color: _selectedItem == item
                                ? Colors.grey[700]
                                : Colors.transparent,
                            child: Text(
                              item,
                              style: widget.textStyle ??
                                  const TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                  ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideDropdown() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  void dispose() {
    _hideDropdown();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (_overlayEntry == null) {
          _showDropdown();
        } else {
          _hideDropdown();
        }
      },
      child: CompositedTransformTarget(
        link: _layerLink,
        child: Container(
          padding: widget.padding ?? const EdgeInsets.symmetric(horizontal: 12),
          margin: widget.margin ?? const EdgeInsets.all(0),
          height: widget.height,
          decoration: BoxDecoration(
            color: widget.backgroundColor,
            borderRadius: BorderRadius.circular(4),
            border: widget.isBorder
                ? Border.all(
              color: Colors.grey[700]!,
              width: 1,
            )
                : null,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(left: 0),
                  child: Text(
                    _selectedItem ?? '',
                    style: widget.textStyle ??
                        const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                        ),
                  ),
                ),
              ),
              Padding(
                padding: widget.iconPadding ?? const EdgeInsets.all(8.0),
                child: widget.dropdownIcon ??
                    const Icon(
                      Icons.arrow_drop_down,
                      color: Colors.white,
                    ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}*/





class CommonDropdown extends StatefulWidget {
  final String? initialText;
  final Icon? dropdownIcon;
  final double? height;
  final List<String> items;
  final Color backgroundColor;
  final Color dropdownBackgroundColor;
  final TextStyle? textStyle;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? textPadding;
  final EdgeInsetsGeometry? iconPadding;
  final ValueChanged<String>? onChanged;
  final bool isBorder;

  const CommonDropdown({
    super.key,
    this.initialText,
    this.dropdownIcon,
    this.height,
    required this.items,
    this.backgroundColor = Colors.black,
    this.dropdownBackgroundColor = Colors.grey,
    this.textStyle,
    this.padding,
    this.margin,
    this.textPadding,
    this.iconPadding,
    this.onChanged,
    this.isBorder = false,
  });

  @override
  _CommonDropdownState createState() => _CommonDropdownState();
}

class _CommonDropdownState extends State<CommonDropdown> {
  String? _selectedItem;
  OverlayEntry? _overlayEntry;
  String? _hoveredItem; 

  @override
  void initState() {
    super.initState();
    _selectedItem = widget.initialText ?? widget.items.first;
    debugPrint("dropdown item :::: ${widget.initialText}");
  }

  void _showDropdown() {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final Offset offset = renderBox.localToGlobal(Offset.zero);
    final Size size = renderBox.size;

    _overlayEntry = OverlayEntry(
      builder: (context) => GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: _hideDropdown, 
        child: Stack(
          children: [
            Positioned(
              left: offset.dx,
              top: offset.dy + size.height,
              width: size.width,
              child: Material(
                color: Colors.transparent,
                child: Container(
                  decoration: BoxDecoration(
                    color: widget.dropdownBackgroundColor,
                    borderRadius: BorderRadius.circular(4),
                    boxShadow: const [
                      BoxShadow(
                        color: Colors.black26,
                        blurRadius: 4,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ListView.builder(
                    padding: EdgeInsets.zero,
                    shrinkWrap: true,
                    itemCount: widget.items.length,
                    itemBuilder: (context, index) {
                      final item = widget.items[index];
                      return MouseRegion(
                        onEnter: (_) {
                          setState(() {
                            _hoveredItem = item;
                          });
                        },
                        onExit: (_) {
                          setState(() {
                            _hoveredItem = null;
                          });
                        },
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedItem = item;
                            });
                            _hideDropdown();
                            if (widget.onChanged != null) {
                              widget.onChanged!(item);
                            }
                          },
                          child: Container(
                            height: widget.height ?? 40,
                            padding: widget.textPadding ??
                                const EdgeInsets.symmetric(horizontal: 16),
                            alignment: Alignment.centerLeft,
                            decoration: BoxDecoration(
                              color: _hoveredItem == item
                                  ? Colors.grey[600] // Hover background color
                                  : (_selectedItem == item
                                  ? Colors.grey[700] // Selected background color
                                  : Colors.transparent),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              item,
                              style: widget.textStyle ??
                                  const TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                  ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideDropdown() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    setState(() {
      _hoveredItem = null;
    });
  }

  @override
  void dispose() {
    _hideDropdown();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (_overlayEntry == null) {
          _showDropdown();
        } else {
          _hideDropdown();
        }
      },
      child: Container(
        padding: widget.padding ?? const EdgeInsets.symmetric(horizontal: 12),
        margin: widget.margin ?? const EdgeInsets.all(0),
        height: widget.height,
        decoration: BoxDecoration(
          color: widget.backgroundColor,
          borderRadius: BorderRadius.circular(4),
          border: widget.isBorder
              ? Border.all(
            color: Colors.grey[700]!,
            width: 1,
          )
              : null,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                _selectedItem ?? '',
                style: widget.textStyle ??
                    const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                    ),
              ),
            ),
            Padding(
              padding: widget.iconPadding ?? const EdgeInsets.all(8.0),
              child: widget.dropdownIcon ??
                  const CommonSVGIcon(
              imageName: 'direction_down',
              imagePath: 'images',
              color: AppThemeColor.commonDropdownArrowColor,
              height: 24,
              width: 24,
              //padding: EdgeInsets.all(12.0),
            ),
            ),
          ],
        ),
      ),
    );
  }
}







