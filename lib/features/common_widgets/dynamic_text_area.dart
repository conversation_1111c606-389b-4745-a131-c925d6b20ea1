import 'package:flutter/material.dart';



class DynamicTextArea extends StatefulWidget {
  final String hintText;
  final double minHeight;
  final double maxHeight;
  final TextStyle? textStyle;
  final InputBorder? border;
  final void Function(String)? onTextChange;

  const DynamicTextArea({
    super.key,
    this.hintText = 'Enter text...',
    this.minHeight = 50.0,
    this.maxHeight = 200.0,
    this.textStyle,
    this.border,
    this.onTextChange,
  });

  @override
  _DynamicTextAreaState createState() => _DynamicTextAreaState();
}

class _DynamicTextAreaState extends State<DynamicTextArea> {
  final TextEditingController _controller = TextEditingController();
  int _maxLines = 1;

  @override
  void initState() {
    super.initState();
    _controller.addListener((){
      if(widget.onTextChange!=null){
        widget.onTextChange!(_controller.text);
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      //constraints: const BoxConstraints.expand(),
      child: Expanded(
        child: TextField(
          controller: _controller,
          maxLines: null, // Allows the text field to expand for multiline input
          //expands: true,
          decoration: InputDecoration(
            hintText: widget.hintText,
            border: InputBorder.none,
            // border: widget.border ??
            //     OutlineInputBorder(
            //       borderRadius: BorderRadius.circular(4.0),
            //       borderSide: const BorderSide(color: Colors.grey, width: 0.5, ),
            //     ),
            contentPadding: const EdgeInsets.all(10.0),
          ),
          style: widget.textStyle ?? const TextStyle(fontSize: 14.0, color: Colors.white),
          keyboardType: TextInputType.multiline,
        ),
      ),
    );
  }
}

