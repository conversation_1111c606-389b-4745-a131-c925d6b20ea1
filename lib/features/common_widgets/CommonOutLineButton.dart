import 'package:flutter/material.dart';

import '../../globals.dart';
import '../../helpers/color_config.dart';


class CommonOutLineButton extends StatelessWidget {
  final String buttonText;
  final Color? backgroundColor;
  final Icon? icon;
  final Color? iconColor;
  final TextStyle? textStyle;
  final VoidCallback? callback;
  double? height;
  final BorderSide side;

  CommonOutLineButton({
    super.key,
    required this.buttonText,
    this.icon,
    this.backgroundColor = AppThemeColor.transparent,
    this.textStyle,
    this.callback,
    this.iconColor,
    this.height = Globals.buttonHeight,
    required this.side
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: MediaQuery.of(context).size.width,
      child: OutlinedButton(onPressed: () {
        callback!();
      }, child: icon!=null ? Row(mainAxisAlignment: MainAxisAlignment.center,
        children: [
          icon!,
          SizedBox(width: 8,),
          Text(buttonText, style: textStyle,),
        ],
      ) : Text(buttonText, style: textStyle,),
        style: OutlinedButton.styleFrom(
          backgroundColor: backgroundColor,
          shadowColor: backgroundColor,
          side: side,
          shape: RoundedRectangleBorder(
              borderRadius: const BorderRadius.all(
                  Radius.circular(12)
              ),

          ),
        ),
      ),
    );
  }
}
