import 'package:flutter/material.dart';

import '../../helpers/color_config.dart';
import '../../helpers/style_config.dart';
import 'CommonSVGIcon.dart';
import 'CommonText.dart';
class CommonAppBar extends StatelessWidget implements PreferredSizeWidget {
  const CommonAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      decoration: BoxDecoration(
        color: AppThemeColor.commonBackground,
        // implement drop shadow here
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1), // rgba(0, 0, 0, 0.10)
            offset: const Offset(0, 8), // (0px, 8px)
            blurRadius: 10, // 10px blur radius
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Container(
                width: 60,
                child: const CommonSVGIcon(
                  imageName: 'home',
                  imagePath: 'images',
                  height: 24,
                  width: 24,
                ),
              ),
              CommonText(
                text: '<PERSON><PERSON>',
                textStyle: mBoldTextStyle16(
                    textSize: 18, textColor: AppThemeColor.white),
              ),
            ],
          ),
          Row(
            children: [
              Container(
                height: 30,
                width: 30,
                child: const CircleAvatar(
                  backgroundColor: Colors.grey,
                  child: Icon(Icons.person, color: Colors.white),
                ),
              ),
              const SizedBox(width: 16),
            ],
          )
        ],
      ),
    );
  }

  @override
  // TODO: implement preferredSize
  Size get preferredSize =>  const Size.fromHeight(60);
}
