import 'package:flutter/material.dart';

import '../../helpers/color_config.dart';
import '../../helpers/style_config.dart';
import 'CommonSVGIcon.dart';
import 'CommonText.dart';

class CommonDropdownWithViews extends StatefulWidget {
  final String? initialText;
  final Icon? dropdownIcon;
  final List<String> items;
  final Color backgroundColor;
  final Color dropdownBackgroundColor;
  final TextStyle? textStyle;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? textPadding;
  final EdgeInsetsGeometry? iconPadding;
  final ValueChanged<String>? onChanged;
  final Map<String, List<Widget>>? dynamicViews;
  final ValueChanged<List<Widget>?>? onViewChanged;

  const CommonDropdownWithViews({
    Key? key,
    this.initialText,
    this.dropdownIcon,
    required this.items,
    this.backgroundColor = Colors.black,
    this.dropdownBackgroundColor = Colors.grey,
    this.textStyle,
    this.padding,
    this.margin,
    this.textPadding,
    this.iconPadding,
    this.onChanged,
    this.dynamicViews,
    this.onViewChanged,
  }) : super(key: key);

  @override
  _CommonDropdownWithViewsState createState() =>
      _CommonDropdownWithViewsState();


}

class _CommonDropdownWithViewsState extends State<CommonDropdownWithViews> {
  String? _selectedItem;
  OverlayEntry? _overlayEntry;
  String? _hoveredItem;

  @override
  void initState() {
    super.initState();
    _selectedItem = widget.initialText ?? (widget.items.isNotEmpty ? widget.items.first : null);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _notifyViewChanged();
    });
  }

  void _notifyViewChanged() {
    if (widget.onViewChanged != null) {
      widget.onViewChanged!(
        widget.dynamicViews != null ? widget.dynamicViews![_selectedItem!] : null,
      );
    }
  }



  void _showDropdown(BuildContext context) {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final Offset offset = renderBox.localToGlobal(Offset.zero);
    final Size size = renderBox.size;

    _overlayEntry = OverlayEntry(
      builder: (context) => GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: _hideDropdown,
        child: Stack(
          children: [
            Positioned(
              left: offset.dx,
              top: offset.dy + size.height,
              width: size.width,
              child: Material(
                color: Colors.transparent,
                child: Container(
                  decoration: BoxDecoration(
                    color: widget.dropdownBackgroundColor,
                    borderRadius: BorderRadius.circular(4),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black26,
                        blurRadius: 4,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ListView.builder(
                    padding: EdgeInsets.zero,
                    shrinkWrap: true,
                    itemCount: widget.items.length,
                    itemBuilder: (context, index) {
                      final item = widget.items[index];
                      return MouseRegion(
                        onEnter: (_) => setState(() => _hoveredItem = item),
                        onExit: (_) => setState(() => _hoveredItem = null),
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedItem = item;
                            });
                            _notifyViewChanged();
                            _hideDropdown();
                            if (widget.onChanged != null) {
                              widget.onChanged!(item);
                            }
                          },
                          child: Container(
                            height: 40,
                            padding: widget.textPadding ??
                                const EdgeInsets.symmetric(horizontal: 16),
                            alignment: Alignment.centerLeft,
                            decoration: BoxDecoration(
                              color: _hoveredItem == item
                                  ? Colors.grey[600] // Hover background
                                  : (_selectedItem == item
                                  ? Colors.grey[700] // Selected background
                                  : Colors.transparent),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              item,
                              style: widget.textStyle ??
                                  const TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                  ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

 /* void _hideDropdown() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    setState(() {
      _hoveredItem = null;
    });
  }*/

  @override
  void dispose() {
    _hideDropdown();
    super.dispose();
  }

  void _hideDropdown() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
    }
    setState(() {
      _hoveredItem = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (_overlayEntry == null) {
          _showDropdown(context);
        } else {
          _hideDropdown();
        }
      },
      child: Container(
        padding: widget.padding ?? const EdgeInsets.symmetric(horizontal: 12),
        margin: widget.margin ?? const EdgeInsets.all(0),
        height: 40,
        decoration: BoxDecoration(
          color: widget.backgroundColor,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              _selectedItem ?? '',
              style: widget.textStyle ??
                  const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                  ),
            ),
            Padding(
              padding: widget.iconPadding ?? const EdgeInsets.all(8.0),
              child: widget.dropdownIcon ??
                  const CommonSVGIcon(
                    imageName: 'direction_down',
                    imagePath: 'images',
                    color: AppThemeColor.commonDropdownArrowColor,
                    height: 24,
                    width: 24,
                    //padding: EdgeInsets.all(12.0),
                  ),
            ),
          ],
        ),
      ),
    );
  }
}


/*class CommonDropdownWithViews extends StatefulWidget {
  final String? initialText;
  final Icon? dropdownIcon;
  final List<String> items;
  final Color backgroundColor;
  final Color dropdownBackgroundColor;
  final TextStyle? textStyle;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? textPadding;
  final EdgeInsetsGeometry? iconPadding;
  final ValueChanged<String>? onChanged;
  final Map<String, List<Widget>>? dynamicViews;
  final ValueChanged<List<Widget>?>? onViewChanged;

  const CommonDropdownWithViews({
    Key? key,
    this.initialText,
    this.dropdownIcon,
    required this.items,
    this.backgroundColor = Colors.black,
    this.dropdownBackgroundColor = Colors.grey,
    this.textStyle,
    this.padding,
    this.margin,
    this.textPadding,
    this.iconPadding,
    this.onChanged,
    this.dynamicViews,
    this.onViewChanged,
  }) : super(key: key);

  @override
  _CommonDropdownWithViewsState createState() =>
      _CommonDropdownWithViewsState();
}

class _CommonDropdownWithViewsState extends State<CommonDropdownWithViews> {
  String? _selectedItem;
  OverlayEntry? _overlayEntry;
  String? _hoveredItem;

  @override
  void initState() {
    super.initState();
    _selectedItem = widget.initialText ?? (widget.items.isNotEmpty ? widget.items.first : null);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _notifyViewChanged();
    });
  }

  void _notifyViewChanged() {
    if (widget.onViewChanged != null) {
      widget.onViewChanged!(
        widget.dynamicViews != null ? widget.dynamicViews![_selectedItem!] : null,
      );
    }
  }

  void _showDropdown(BuildContext context) {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final Offset offset = renderBox.localToGlobal(Offset.zero);
    final Size size = renderBox.size;

    _overlayEntry = OverlayEntry(
      builder: (context) => GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: _hideDropdown,
        child: Stack(
          children: [
            Positioned(
              left: offset.dx,
              top: offset.dy + size.height,
              width: size.width,
              child: Material(
                color: Colors.transparent,
                child: Container(
                  decoration: BoxDecoration(
                    color: widget.dropdownBackgroundColor,
                    borderRadius: BorderRadius.circular(4),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black26,
                        blurRadius: 4,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ListView.builder(
                    padding: EdgeInsets.zero,
                    shrinkWrap: true,
                    itemCount: widget.items.length,
                    itemBuilder: (context, index) {
                      final item = widget.items[index];
                      return MouseRegion(
                        onEnter: (_) => setState(() => _hoveredItem = item),
                        onExit: (_) => setState(() => _hoveredItem = null),
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedItem = item;
                            });
                            _notifyViewChanged();
                            _hideDropdown();
                            if (widget.onChanged != null) {
                              widget.onChanged!(item);
                            }
                          },
                          child: Container(
                            height: 40,
                            padding: widget.textPadding ??
                                const EdgeInsets.symmetric(horizontal: 16),
                            alignment: Alignment.centerLeft,
                            decoration: BoxDecoration(
                              color: _hoveredItem == item
                                  ? Colors.grey[600] // Hover background
                                  : (_selectedItem == item
                                  ? Colors.grey[700] // Selected background
                                  : Colors.transparent),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              item,
                              style: widget.textStyle ??
                                  const TextStyle(
                                    color: Colors.white, fontFamily: 'inter',
                                    fontSize: 16,
                                  ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideDropdown() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    setState(() {
      _hoveredItem = null;
    });
  }

  @override
  void dispose() {
    _hideDropdown();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (_overlayEntry == null) {
          _showDropdown(context);
        } else {
          _hideDropdown();
        }
      },
      child: Container(
        padding: widget.padding ?? const EdgeInsets.symmetric(horizontal: 12),
        margin: widget.margin ?? const EdgeInsets.all(0),
        height: 40,
        decoration: BoxDecoration(
          color: widget.backgroundColor,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                _selectedItem ?? '',
                style: widget.textStyle ??
                    const TextStyle(
                      color: Colors.white, fontFamily: 'inter',
                      fontSize: 14,
                    ),
              ),
            ),
            Padding(
              padding: widget.iconPadding ?? const EdgeInsets.all(8.0),
              child: widget.dropdownIcon ??
                  const CommonSVGIcon(
                    imageName: 'direction_down',
                    imagePath: 'images',
                    color: AppThemeColor.commonDropdownArrowColor,
                    height: 24,
                    width: 24,
                    //padding: EdgeInsets.all(12.0),
                  ),
            ),
          ],
        ),
      ),
    );
  }
}*/


