import 'package:flutter/material.dart';
import 'package:postman_flutter/helpers/color_config.dart';

import '../../helpers/style_config.dart';
import 'CommonText.dart';

class CustomCheckbox extends StatelessWidget {
  final bool value;
  final String label;
  final ValueChanged<bool> onChanged;
  double? checkboxSize;
  double? checkIconSize;

   CustomCheckbox({
    super.key,
    required this.value,
    required this.label,
    required this.onChanged,
    this.checkboxSize = 16.0,
    this.checkIconSize = 12.0
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onChanged(!value);
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: checkboxSize,
            width: checkboxSize,
            decoration: BoxDecoration(
              color: value ? AppThemeColor.checkBoxFillColor : Colors.transparent,
              border: Border.all(
                color: AppThemeColor.commonBorderColor,
                width: 1.0,
              ),
              borderRadius: BorderRadius.circular(4.0),
            ),
            child: value
                ? Icon(
              Icons.check,
              color: Colors.white,
              size: checkIconSize,
            )
                : null,
          ),

          if(label.isNotEmpty)...[
          SizedBox(width: 8.0),

          CommonText(
            text: label,
            textStyle: mRegularTextStyle16(
                textSize: 11, textColor: AppThemeColor.checkBoxTextColor),
          ),
        ]
        ],
      ),
    );
  }
}
