import 'package:flutter/material.dart';
import 'CommonText.dart';
class CommonTextButton extends StatelessWidget {
  final TextStyle? textStyle;
  final TextAlign? textAlign;
  final String text;
  final bool? visibility;
  final bool enabled;
  final VoidCallback? callback;

  const CommonTextButton({
    super.key,
    this.textStyle,
    this.textAlign,
    required this.text,
    this.visibility = false,
    this.enabled = true,
    this.callback,
  });

  @override
  Widget build(BuildContext context) {
    return visibility == false
        ? TextButton(
      onPressed: enabled ? callback : null,
      style: TextButton.styleFrom(
        minimumSize: Size.zero,
        padding: EdgeInsets.zero,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
      child: CommonText(
        textAlign: textAlign,
        text: text,
        textStyle: textStyle?.copyWith(
          color: enabled
              ? textStyle?.color
              : Colors.grey,
        ),
      ),
    )
        : const SizedBox();
  }
}

