import 'package:flutter/material.dart';

import '../../helpers/color_config.dart';
import '../../helpers/style_config.dart';
import 'CommonSVGIcon.dart';
import 'CommonText.dart';


class InfoIconButton extends StatefulWidget {
  final String? hoverText; // Optional hoverText
  final bool onHoverInfoText; // Flag to enable/disable hover text


  const InfoIconButton({
    Key? key,
    this.hoverText, // hoverText is optional
    this.onHoverInfoText = false, // Default to false if not specified
  }) : super(key: key);

  @override
  _InfoIconButtonState createState() => _InfoIconButtonState();
}

class _InfoIconButtonState extends State<InfoIconButton> {
  bool _isHovering = false;
  final GlobalKey _iconKey = GlobalKey(); // GlobalKey for accessing the position of the icon
  OverlayEntry? _overlayEntry;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) {
        if (widget.onHoverInfoText && widget.hoverText != null) {
          setState(() {
            _isHovering = true;
          });
          // Show the hover text in the overlay
          _showHoverText(context);
        }
      },
      onExit: (_) {
        setState(() {
          _isHovering = false;
        });
        // Remove the hover text from the overlay
        _hideHoverText();
      },
      child: GestureDetector(
        key: _iconKey, // Assign the GlobalKey to the icon
        child: const CommonSVGIcon(
          imageName: 'info',
          imagePath: 'images',
          color: AppThemeColor.infoIconColor,
          height: 16,
          width: 16,
        ),
      ),
    );
  }

  // Method to show hover text in the overlay
  void _showHoverText(BuildContext context) {
    if (widget.hoverText == null) return;

    // Get the RenderBox of the icon to calculate its position
    final RenderBox renderBox = _iconKey.currentContext!.findRenderObject() as RenderBox;
    final iconPosition = renderBox.localToGlobal(Offset.zero); // Get the position of the icon in global coordinates
    final iconHeight = renderBox.size.height;

    final overlay = Overlay.of(context);
    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: iconPosition.dx, // Position horizontally at the icon's x-coordinate
        top: iconPosition.dy - 45, // Position vertically above the icon (adjust value if needed)
        child: Material(
          color: AppThemeColor.lightBackground,
          borderRadius: BorderRadius.circular(4),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
            child: CommonText(
              text: widget.hoverText!,
              textStyle: mRegularTextStyle16(
                textSize: 12,
                textColor: AppThemeColor.white,
              ),
            ),
          ),
        ),
      ),
    );

    overlay?.insert(_overlayEntry!);
  }

  // Method to hide hover text from the overlay
  void _hideHoverText() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
}


