import 'package:flutter/material.dart';

import '../../globals.dart';
import '../../helpers/color_config.dart';
import 'CommonText.dart';


class CommonButton extends StatelessWidget {
  final String buttonText;
  final Color? backgroundColor;
  final Icon? icon;
  final Color? iconColor;
  final TextStyle? textStyle;
  final VoidCallback? callback;
  double? height;
  double? width;

  CommonButton({
    super.key,
    required this.buttonText,
    this.icon,
    this.backgroundColor = AppThemeColor.primaryColor,
    this.textStyle,
    this.callback,
    this.iconColor,
    this.height = Globals.buttonHeight,
    this.width
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: width ?? MediaQuery.of(context).size.width,
      child: ElevatedButton(onPressed: () {
        callback!();
      }, style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor,
          shadowColor: backgroundColor,
          shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                  Radius.circular(4)
              )
          ),
        ),
        child: icon!=null ? Row(mainAxisAlignment: MainAxisAlignment.center,
          children: [
            icon!,
            const SizedBox(width: 8,),
            //Text(buttonText, style: textStyle,),
            CommonText(
              text: buttonText,
              textStyle: textStyle,
            ),
          ],
        ) :  CommonText(
          text: buttonText,
          textStyle: textStyle,
        ),
      ),
    );
  }
}
