import 'package:flutter/material.dart';
import 'CommonText.dart';

class CommonIconTextButton extends StatelessWidget {
  final TextStyle? textStyle;
  final TextAlign? textAlign;
  final String text;
  final bool? visibility;
  final VoidCallback? callback;
  final Widget? icon;
  final double iconSpacing;
  final EdgeInsetsGeometry? padding;

  const CommonIconTextButton({
    super.key,
    this.textStyle,
    this.textAlign,
    required this.text,
    this.visibility = false,
    this.callback,
    this.icon,
    this.iconSpacing = 8.0,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return visibility == false
        ? TextButton(
      onPressed: () {
        if (callback != null) callback!();
      },
      style: TextButton.styleFrom(
        padding: padding ?? EdgeInsets.symmetric(horizontal: 0.0, vertical: 4.0),
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            icon!,
            SizedBox(width: iconSpacing), // Space between icon and text
          ],
          CommonText(
            textAlign: textAlign,
            text: text,
            textStyle: textStyle,
          ),
        ],
      ),
    )
        : const SizedBox();
  }
}

