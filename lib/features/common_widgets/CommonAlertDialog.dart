import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/home_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/home_state.dart';

import '../../globals.dart';
import '../../helpers/color_config.dart';
import '../../helpers/style_config.dart';
import 'CommonButton.dart';
import 'CommonOutLineButton.dart';
import 'CommonSVGIcon.dart';
import 'CommonText.dart';
import '../common_widgets/CommonTextFormField.dart';

class CommonAlertDialog {
  static void show({
    required BuildContext context,
    required String title,
    required String buttonCreateText,
    required String description,
    required List<Widget> inputFields,
    required VoidCallback onCancel,
    required VoidCallback onCreate,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: AppThemeColor.lightBackground,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4.0),
          ),
          child: Container(
            padding: const EdgeInsets.only(
                left: 35.0, right: 35.0, top: 30, bottom: 30),
            width: 430,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: AppThemeColor.lightBackground,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CommonText(
                      text: title,
                      textStyle: mBoldTextStyle16(
                          textSize: 24, textColor: AppThemeColor.white),
                    ),

                    InkWell(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Container(
                        // height: 30,
                        // width: 30,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4),
                         // color: AppThemeColor.crossButtonBackground,
                        ),
                        child: const CommonSVGIcon(
                          imageName: 'remove',
                          imagePath: 'images',
                          height: 24,
                          width: 24,
                        ),
                      ),
                    )
                  ],
                ),
                if(description.isNotEmpty)...[
                SizedBox(height: 24),
                CommonText(
                  text: description,
                  maxLine: 3,
                  textStyle: mRegularTextStyle16(
                      textSize: 14,
                      textColor: AppThemeColor.dialogDescriptionTextColor),
                ),],
                const SizedBox(height: 24),
                ...inputFields.map((field) => Padding(
                  padding: const EdgeInsets.only(bottom: 24.0),
                  child: field,
                )),
                Row(
                  children: [
                    Expanded(
                        child: CommonButton(
                          buttonText: 'Cancel',
                          callback: onCancel,
                          height: 40,
                          backgroundColor: AppThemeColor.white,
                          textStyle: const TextStyle(color: AppThemeColor.black),
                        )),
                    const SizedBox(width: 7),
                    Expanded(
                        child: CommonButton(
                          buttonText: buttonCreateText,
                          callback: onCreate,
                          height: 40,
                          backgroundColor: AppThemeColor.buttonBlue,
                          textStyle: const TextStyle(color: AppThemeColor.white),
                        )),
                  ],
                )
              ],
            ),
          ),
        );
      },
    );
  }
}

class CommonAlertDialogForSaveRequest {
  static void show({
    required BuildContext context,
    required String title,
    required String buttonCreateText,
    required String description,
    required List<Widget> inputFields,
    required VoidCallback onCancel,
    required VoidCallback onCreate,
    required VoidCallback onTapNewFolder,
    required VoidCallback onTapNewCollection,
    required bool showNewCollection,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: AppThemeColor.lightBackground,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4.0),
          ),
          child: BlocBuilder<HomeBloc, HomeState>(
          builder: (context, state) {
            return Container(
            padding: const EdgeInsets.only(
                left: 35.0, right: 35.0, top: 30, bottom: 30),
            width: 630,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: AppThemeColor.lightBackground,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CommonText(
                      text: title,
                      textStyle: mBoldTextStyle16(
                          textSize: 24, textColor: AppThemeColor.white),
                    ),
                    InkWell(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Container(
                        // height: 30,
                        // width: 30,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4),
                          //color: AppThemeColor.crossButtonBackground,
                        ),
                        child: const CommonSVGIcon(
                          imageName: 'remove',
                          imagePath: 'images',
                          height: 24,
                          width: 24,
                        ),
                      ),
                    )
                  ],
                ),
                if(description.isNotEmpty)...[
                  SizedBox(height: 24),
                  CommonText(
                    text: description,
                    textStyle: mRegularTextStyle16(
                        textSize: 14,
                        textColor: AppThemeColor.dialogDescriptionTextColor),
                  ),],
                const SizedBox(height: 24),
                ...inputFields.map((field) => Padding(
                  padding: const EdgeInsets.only(bottom: 24.0),
                  child: field,
                )),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    if (state.forCollection) ...[
                      CommonText(
                        text: 'New Collection',
                        onTap: onTapNewCollection,
                        underlineOnHover: true,
                        underlineColor: AppThemeColor.white,
                        textStyle: mMediumTextStyle16(
                            textSize: 14, textColor: AppThemeColor.white),
                      )] else ...[
                      CommonText(
                      text: 'New Folder',
                      onTap: onTapNewFolder,
                        underlineOnHover: true,
                        underlineColor: AppThemeColor.white,
                      textStyle: mMediumTextStyle16(
                          textSize: 14, textColor: AppThemeColor.white),
                      ),],
                    Row(
                      children: [
                        CommonButton(
                          buttonText: 'Cancel',
                          callback: onCancel,
                          width: 100,
                          height: 40,
                          backgroundColor: AppThemeColor.white,
                          textStyle: const TextStyle(color: AppThemeColor.black),
                        ),
                        const SizedBox(width: 7),
                        CommonButton(
                          buttonText: buttonCreateText,
                          callback: onCreate,
                          width: 100,
                          height: 40,
                          backgroundColor: AppThemeColor.buttonBlue,
                          textStyle: const TextStyle(color: AppThemeColor.white),
                        ),
                      ],
                    ),
                  ],
                )
              ],
            ),
          );
  },
),
        );
      },
    );
  }
}

class CommonDeleteAlertDialog {
  static void show({
    required BuildContext context,
    required String title,
    required String buttonCreateText,
    required String description,
    required List<Widget> inputFields,
    required VoidCallback onCancel,
    required VoidCallback onCreate,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: AppThemeColor.lightBackground,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4.0),
          ),
          child: Container(
            padding: const EdgeInsets.only(
                left: 35.0, right: 35.0, top: 24, bottom: 24),
            width: 430,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: AppThemeColor.lightBackground,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [

                const CommonSVGIcon(
                  imageName: 'warning_circle',
                  imagePath: 'images',
                  height: 72,
                  width: 72,
                  padding: EdgeInsets.all(0),
                ),

                SizedBox(height: 18,),


                CommonText(
                  text: title,
                  textStyle: mBoldTextStyle16(
                      textSize: 18, textColor: AppThemeColor.white),
                ),

                if(description.isNotEmpty)...[
                  SizedBox(height: 18),
                  CommonText(
                    text: description,
                    maxLine: 3,
                    textStyle: mRegularTextStyle16(
                        textSize: 14,
                        textColor: AppThemeColor.dialogDescriptionTextColor),
                  ),],
                const SizedBox(height: 24),
                ...inputFields.map((field) => Padding(
                  padding: const EdgeInsets.only(bottom: 24.0),
                  child: field,
                )),
                Row(
                  children: [
                    Expanded(
                        child: CommonButton(
                          buttonText: 'Cancel',
                          callback: onCancel,
                          height: 40,
                          backgroundColor: AppThemeColor.white,
                          textStyle: const TextStyle(color: AppThemeColor.black),
                        )),
                    const SizedBox(width: 7),
                    Expanded(
                        child: CommonButton(
                          buttonText: buttonCreateText,
                          callback: onCreate,
                          height: 40,
                          backgroundColor: AppThemeColor.buttonBlue,
                          textStyle: const TextStyle(color: AppThemeColor.white),
                        )),
                  ],
                )
              ],
            ),
          ),
        );
      },
    );
  }
}

class CommonCloseTabAlertDialog {
  static void show({
    required BuildContext context,
    required String title,
    required String buttonCreateText,
    required String description,
    required List<Widget> inputFields,
    required VoidCallback onCancel,
    required VoidCallback onSaveChanges,
    required VoidCallback onDontSave,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: AppThemeColor.lightBackground,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4.0),
          ),
          child: Container(
            padding: const EdgeInsets.only(
                left: 35.0, right: 35.0, top: 24, bottom: 24),
            width: 450,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: AppThemeColor.lightBackground,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [

                const CommonSVGIcon(
                  imageName: 'warning_circle',
                  imagePath: 'images',
                  height: 72,
                  width: 72,
                  padding: EdgeInsets.all(0),
                ),

                SizedBox(height: 18,),


                CommonText(
                  text: title,
                  textStyle: mBoldTextStyle16(
                      textSize: 18, textColor: AppThemeColor.white),
                ),

                if(description.isNotEmpty)...[
                  SizedBox(height: 18),
                  CommonText(
                    text: description,
                    maxLine: 3,
                    textStyle: mRegularTextStyle16(
                        textSize: 14,
                        textColor: AppThemeColor.dialogDescriptionTextColor),
                  ),],

                const SizedBox(height: 24),

                ...inputFields.map((field) => Padding(
                  padding: const EdgeInsets.only(bottom: 24.0),
                  child: field,
                )),

                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [

                    CommonButton(
                      buttonText: "Discard",
                      callback: onDontSave,
                      height: 40,
                      width: 100,
                      backgroundColor: AppThemeColor.white,
                      textStyle: const TextStyle(color: AppThemeColor.black),
                    ),


                    Row(
                      children: [
                        CommonButton(
                          buttonText: 'Cancel',
                          callback: onCancel,
                          height: 40,
                          width: 100,
                          backgroundColor: AppThemeColor.white,
                          textStyle: const TextStyle(color: AppThemeColor.black),
                        ),
                        SizedBox(width: 5,),
                        CommonButton(
                          buttonText: buttonCreateText,
                          callback: onSaveChanges,
                          width: 100,
                          height: 40,
                          backgroundColor: AppThemeColor.buttonBlue,
                          textStyle: const TextStyle(color: AppThemeColor.white),
                        ),
                      ],
                    ),
                  ],
                )
              ],
            ),
          ),
        );
      },
    );
  }
}





