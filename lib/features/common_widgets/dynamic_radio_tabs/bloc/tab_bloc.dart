import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:postman_flutter/features/common_widgets/dynamic_radio_tabs/bloc/tab_event.dart';
import 'package:postman_flutter/features/common_widgets/dynamic_radio_tabs/bloc/tab_state.dart';

class TabBloc extends Bloc<TabEvent, TabState> {
  TabBloc() : super(TabInitial(0)) {
    on<UpdateTabEvent>((event, emit) {
      emit(TabUpdated(event.tabIndex));
    });
  }
}
