import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';


import '../../../helpers/color_config.dart';
import '../../../helpers/style_config.dart';
import '../CommonText.dart';
import 'bloc/tab_bloc.dart';
import 'bloc/tab_event.dart';
import 'bloc/tab_state.dart';


class DynamicRadioTabsWidget extends StatelessWidget {
  final List<String> tabTitles;
  final List<Widget> tabViews;
  final int defaultSelectedTab;

  const DynamicRadioTabsWidget({
    Key? key,
    required this.tabTitles,
    required this.tabViews,
    this.defaultSelectedTab = 0,
  })  : assert(tabTitles.length == tabViews.length, 'tabTitles and tabViews must have the same length'),
        super(key: key);

  @override
  Widget build(BuildContext context) {
    // Set the default selected tab only if it's the initial state
    final tabBloc = context.read<TabBloc>();
    if (tabBloc.state is TabInitial && defaultSelectedTab != 0) {
      tabBloc.add(UpdateTabEvent(defaultSelectedTab));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        BlocBuilder<TabBloc, TabState>(
          builder: (context, state) {
            final selectedTabIndex = state is TabUpdated ? state.tabIndex : defaultSelectedTab;

            return Row(
              children: List.generate(
                tabTitles.length,
                    (index) => GestureDetector(
                  onTap: () {
                    context.read<TabBloc>().add(UpdateTabEvent(index));
                  },
                  child: Padding(
                    padding: const EdgeInsets.only(right: 24.0),
                    child: Row(
                      children: [
                        Container(
                          height: 18,
                          width: 18,
                          decoration: const ShapeDecoration(
                            shape: CircleBorder(),
                            color: Colors.white,
                          ),
                          child: Center(
                            child: Container(
                              width: 13,
                              height: 13,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: selectedTabIndex == index
                                    ? Colors.blue
                                    : Colors.white,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        CommonText(
                          text: tabTitles[index],
                          textAlign: TextAlign.left,
                          textStyle: mMediumTextStyle16(
                            textSize: 13,
                            textColor: AppThemeColor.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
        const SizedBox(height: 16),
        BlocBuilder<TabBloc, TabState>(
          builder: (context, state) {
            final selectedTabIndex = state is TabUpdated ? state.tabIndex : defaultSelectedTab;

            return IndexedStack(
              index: selectedTabIndex,
              children: tabViews,
            );
          },
        ),
      ],
    );
  }
}





/*class DynamicRadioTabsWidget extends StatefulWidget {
  final List<String> tabTitles;
  final List<Widget> tabViews;
  final int defaultSelectedTab;
  final ValueChanged<int>? onTabChanged; // Add this callback

  const DynamicRadioTabsWidget({
    Key? key,
    required this.tabTitles,
    required this.tabViews,
    this.defaultSelectedTab = 0,
    this.onTabChanged, // Initialize the callback
  })  : assert(tabTitles.length == tabViews.length, 'tabTitles and tabViews must have the same length'),
        super(key: key);

  @override
  _DynamicRadioTabsWidgetState createState() => _DynamicRadioTabsWidgetState();
}

class _DynamicRadioTabsWidgetState extends State<DynamicRadioTabsWidget> {
  late int _selectedTabIndex;

  @override
  void initState() {
    super.initState();
    _selectedTabIndex = widget.defaultSelectedTab; // Set default selected tab
  }

  void _onTabSelected(int index) {
    setState(() {
      _selectedTabIndex = index;
    });
    widget.onTabChanged?.call(index); // Notify parent widget about the tab change
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: List.generate(
            widget.tabTitles.length,
                (index) => GestureDetector(
              onTap: () => _onTabSelected(index),
              child: Padding(
                padding: const EdgeInsets.only(right: 24.0),
                child: Row(
                  children: [
                    // Circular Radio Button
                    Container(
                      height: 18,
                      width: 18,
                      decoration: const ShapeDecoration(
                        shape: CircleBorder(),
                        color: Colors.white,
                      ),
                      child: Center(
                        child: Container(
                          width: 13,
                          height: 13,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: _selectedTabIndex == index
                                ? Colors.blue
                                : Colors.white,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8), // Space between circle and text

                    // Tab Title
                    CommonText(
                      text: widget.tabTitles[index],
                      textAlign: TextAlign.left,
                      textStyle: mMediumTextStyle16(
                        textSize: 13,
                        textColor: AppThemeColor.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 16),
        // Use IndexedStack to preserve state of tab views
        Container(
          child: IndexedStack(
            index: _selectedTabIndex,
            children: widget.tabViews,
          ),
        ),
      ],
    );
  }
}*/




/*class DynamicRadioTabsWidget extends StatefulWidget {
  final List<String> tabTitles;
  final List<Widget> tabViews;
  final int defaultSelectedTab;

  const DynamicRadioTabsWidget({
    Key? key,
    required this.tabTitles,
    required this.tabViews,
    this.defaultSelectedTab = 0,
  })  : assert(tabTitles.length == tabViews.length, 'tabTitles and tabViews must have the same length'),
        super(key: key);

  @override
  _DynamicRadioTabsWidgetState createState() => _DynamicRadioTabsWidgetState();
}

class _DynamicRadioTabsWidgetState extends State<DynamicRadioTabsWidget> {
  late int _selectedTabIndex;

  @override
  void initState() {
    super.initState();
    _selectedTabIndex = widget.defaultSelectedTab; // Set default selected tab
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: List.generate(
            widget.tabTitles.length,
                (index) => GestureDetector(
              onTap: () {
                setState(() {
                  _selectedTabIndex = index;
                });
              },
              child: Padding(
                padding: const EdgeInsets.only(right: 24.0),
                child: Row(
                  children: [
                    // Circular Radio Button
                    Container(
                      height: 18,
                      width: 18,
                      decoration: const ShapeDecoration(
                        shape: CircleBorder(),
                        color: Colors.white,
                      ),
                      child: Center(
                        child: Container(
                          width: 13,
                          height: 13,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: _selectedTabIndex == index
                                ? Colors.blue
                                : Colors.white,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8), // Space between circle and text

                    // Tab Title
                    CommonText(
                      text: widget.tabTitles[index],
                      textAlign: TextAlign.left,
                      textStyle: mMediumTextStyle16(
                          textSize: 13, textColor: AppThemeColor.white),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 16),
        // Use IndexedStack to preserve state of tab views
        Container(
          child: IndexedStack(
            index: _selectedTabIndex,
            children: widget.tabViews,
          ),
        ),
      ],
    );
  }
}*/



