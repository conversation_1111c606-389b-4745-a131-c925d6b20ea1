import 'package:flutter/material.dart';

import '../../globals.dart';


/*class CommonText extends StatefulWidget {
  final TextStyle? textStyle;
  final TextAlign? textAlign;
  final String text;
  final bool? visibility;
  final int? maxLine;
  final TextOverflow overflow;
  final bool isEditable;
  final ValueChanged<String>? onTextChanged; // Callback for updated text

  const CommonText({
    super.key,
    this.textStyle,
    this.textAlign,
    required this.text,
    this.visibility = true,
    this.maxLine,
    this.overflow = TextOverflow.ellipsis,
    this.isEditable = false,
    this.onTextChanged, // New parameter
  });

  @override
  _CommonTextState createState() => _CommonTextState();
}

class _CommonTextState extends State<CommonText> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  late ValueNotifier<bool> _isEditing;
  late ValueNotifier<String> _updatedText;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.text);
    _focusNode = FocusNode();
    _isEditing = ValueNotifier<bool>(false);
    _updatedText = ValueNotifier<String>(widget.text);

    // Auto-save when focus is lost
    _focusNode.addListener(() {
      if (!_focusNode.hasFocus) {
        _saveText();
      }
    });
  }

  void _saveText() {
    _updatedText.value = _controller.text;
    _isEditing.value = false;

    // Notify parent widget about the text change
    if (widget.onTextChanged != null) {
      widget.onTextChanged!(_controller.text);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _isEditing.dispose();
    _updatedText.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.visibility == false) {
      return const SizedBox();
    }

    return GestureDetector(
      onTap: widget.isEditable
          ? () {
        _isEditing.value = true;
        _focusNode.requestFocus();
      }
          : null,
      child: ValueListenableBuilder<bool>(
        valueListenable: _isEditing,
        builder: (context, isEditing, child) {
          return isEditing
              ? SizedBox(
            width: double.infinity, // Ensures finite width
            child: TextField(
              controller: _controller,
              focusNode: _focusNode,
              autofocus: true,
              style: widget.textStyle,
              maxLines: widget.maxLine,
              textAlign: widget.textAlign ?? TextAlign.start,
              decoration: const InputDecoration(border: InputBorder.none),
              onSubmitted: (value) => _saveText(),
            ),
          )
              : ValueListenableBuilder<String>(
            valueListenable: _updatedText,
            builder: (context, updatedText, child) {
              return Text(
                updatedText,
                maxLines: widget.maxLine,
                overflow: widget.overflow,
                style: widget.textStyle,
                textAlign: widget.textAlign,
                softWrap: true,
              );
            },
          );
        },
      ),
    );
  }
}*/




/*class CommonText extends StatelessWidget {
  final TextStyle? textStyle;
  final TextAlign? textAlign;
  final String text;
  final bool? visibility;
  final int? maxLine;
  final TextOverflow overflow;
  final VoidCallback? onTap; // onTap callback for click functionality
  final bool underline; // Option to enable underline
  final Color? underlineColor; // Custom underline color

  const CommonText({
    super.key,
    this.textStyle,
    this.textAlign,
    required this.text,
    this.visibility = true,
    this.maxLine = Globals.textMaxLines,
    this.overflow = TextOverflow.ellipsis,
    this.onTap,
    this.underline = false, // Default to false
    this.underlineColor, // Custom underline color
  });

  @override
  Widget build(BuildContext context) {
    if (visibility != true) {
      return const SizedBox();
    }

    TextStyle effectiveTextStyle = textStyle ?? const TextStyle();

    // Apply underline with custom color if needed
    if (underline) {
      effectiveTextStyle = effectiveTextStyle.copyWith(
        decoration: TextDecoration.underline,
        decorationColor: underlineColor ?? effectiveTextStyle.color, // Use provided color or text color
      );
    }

    Widget textWidget = Text(
      text,
      maxLines: maxLine,
      overflow: overflow,
      style: effectiveTextStyle,
      textAlign: textAlign,
      softWrap: true,
    );

    // If onTap is provided, wrap with GestureDetector
    return onTap != null
        ? InkWell(
      onTap: onTap,
      child: textWidget,
    )
        : textWidget;
  }
}*/


/*class CommonText extends StatelessWidget {
  final TextStyle? textStyle;
  final TextAlign? textAlign;
  final String text;
  final bool? visibility;
  final int? maxLine;
  final TextOverflow overflow;
  final VoidCallback? onTap;
  final bool underline;
  final Color? underlineColor;
  final bool underlineOnHover;

  const CommonText({
    super.key,
    this.textStyle,
    this.textAlign,
    required this.text,
    this.visibility = true,
    this.maxLine,
    this.overflow = TextOverflow.ellipsis,
    this.onTap,
    this.underline = false,
    this.underlineColor,
    this.underlineOnHover = false,
  });

  @override
  Widget build(BuildContext context) {
    if (visibility != true) {
      return const SizedBox();
    }

    final ValueNotifier<bool> isHovered = ValueNotifier<bool>(false);

    return MouseRegion(
      onEnter: (event) => isHovered.value = true,
      onExit: (event) => isHovered.value = false,
      child: ValueListenableBuilder<bool>(
        valueListenable: isHovered,
        builder: (context, hovered, child) {
          TextStyle effectiveTextStyle = textStyle ?? const TextStyle();

          if (underline || (underlineOnHover && hovered)) {
            effectiveTextStyle = effectiveTextStyle.copyWith(
              decoration: TextDecoration.underline,
              decorationColor: underlineColor ?? effectiveTextStyle.color,
            );
          }

          Widget textWidget = Text(
            text,
            maxLines: maxLine,
            overflow: overflow,
            style: effectiveTextStyle,
            textAlign: textAlign,
            softWrap: true,
          );

          return onTap != null
              ? InkWell(
            onTap: onTap,
            child: textWidget,
          )
              : textWidget;
        },
      ),
    );
  }
}*/

class CommonText extends StatelessWidget {
  final TextStyle? textStyle;
  final TextAlign? textAlign;
  final String text;
  final bool? visibility;
  final int? maxLine;
  final TextOverflow overflow;
  final VoidCallback? onTap;
  final bool underline;
  final Color? underlineColor;
  final bool underlineOnHover;
  final int? maxLength;

  const CommonText({
    super.key,
    this.textStyle,
    this.textAlign,
    required this.text,
    this.visibility = true,
    this.maxLine,
    this.overflow = TextOverflow.ellipsis,
    this.onTap,
    this.underline = false,
    this.underlineColor,
    this.underlineOnHover = false,
    this.maxLength,
  });

  @override
  Widget build(BuildContext context) {
    if (visibility != true) {
      return const SizedBox();
    }

    final ValueNotifier<bool> isHovered = ValueNotifier<bool>(false);
    String displayedText = text;

    if (maxLength != null && text.length > maxLength!) {
      displayedText = text.substring(0, maxLength!) + '...';
    }

    return MouseRegion(
      onEnter: (event) => isHovered.value = true,
      onExit: (event) => isHovered.value = false,
      child: ValueListenableBuilder<bool>(
        valueListenable: isHovered,
        builder: (context, hovered, child) {
          TextStyle effectiveTextStyle = textStyle ?? const TextStyle();

          if (underline || (underlineOnHover && hovered)) {
            effectiveTextStyle = effectiveTextStyle.copyWith(
              decoration: TextDecoration.underline,
              decorationColor: underlineColor ?? effectiveTextStyle.color,
            );
          }

          Widget textWidget = Text(
            displayedText,
            maxLines: maxLine,
            overflow: overflow,
            style: effectiveTextStyle,
            textAlign: textAlign,
            softWrap: true,
          );

          return onTap != null
              ? InkWell(
            onTap: onTap,
            child: textWidget,
          )
              : textWidget;
        },
      ),
    );
  }
}




