part of 'collection_bloc.dart';

sealed class CollectionState extends Equatable {
  const CollectionState();

  @override
  List<Object?> get props => [];
}

final class CollectionInitial extends CollectionState {}

class CollectionLoading extends CollectionState {}

class CollectionLoaded extends CollectionState {
  final List<Collection> collections;
  const CollectionLoaded(this.collections);

  @override
  List<Object> get props => [collections];
}

class CollectionError extends CollectionState {
  final String message;

  const CollectionError(this.message);

  @override
  List<Object> get props => [message];
}

class CollectionDetailInitial extends CollectionState {}

class CollectionDetailLoading extends CollectionState {}

class CollectionDetailLoaded extends CollectionState {
  final CollectionDetail? collectionDetail;

  const CollectionDetailLoaded(this.collectionDetail);

  @override
  List<Object?> get props => [collectionDetail];
}

class CollectionDetailError extends CollectionState {
  final String message;

  const CollectionDetailError(this.message);

  @override
  List<Object> get props => [message];
}

class CollectionCreating extends CollectionState {}

class CollectionCreated extends CollectionState {
  final String message;
  final Collection newCollection;

  const CollectionCreated(this.message, this.newCollection);

  @override
  List<Object> get props => [message, newCollection];
}

class CollectionCreationError extends CollectionState {
  final String message;

  const CollectionCreationError(this.message);

  @override
  List<Object> get props => [message];
}

class CollectionDeleting extends CollectionState {}

class CollectionDeleted extends CollectionState {
  final String message;
  final int deletedCollectionId;

  const CollectionDeleted(this.message, this.deletedCollectionId);

  @override
  List<Object> get props => [message, deletedCollectionId];
}

class CollectionDeletionError extends CollectionState {
  final String message;

  const CollectionDeletionError(this.message);

  @override
  List<Object> get props => [message];
}

class CollectionUpdating extends CollectionState {}

class CollectionUpdated extends CollectionState {
  final String message;
  final Collection updatedCollection;

  const CollectionUpdated(this.message, this.updatedCollection);

  @override
  List<Object> get props => [message, updatedCollection];
}

class CollectionUpdateError extends CollectionState {
  final String message;

  const CollectionUpdateError(this.message);

  @override
  List<Object> get props => [message];
}
