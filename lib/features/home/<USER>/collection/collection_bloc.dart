import 'dart:math';

import 'package:bloc/bloc.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:equatable/equatable.dart';
import 'package:postman_flutter/model/collection_detail_model.dart';
import 'package:postman_flutter/model/collection_model.dart';
import 'package:postman_flutter/repo/collectionRepo.dart';

part 'collection_event.dart';
part 'collection_state.dart';

class CollectionBloc extends Bloc<CollectionEvent, CollectionState> {
  final CollectionRepository repository;

  CollectionBloc({required this.repository}) : super(CollectionInitial()) {
    on<FetchCollections>(_onFetchCollections);
    on<FetchCollectionDetail>(_onFetchCollectionDetail);
    on<CreateCollection>(_onCreateCollection);
    on<DeleteCollection>(_onDeleteCollection);
    on<UpdateCollection>(_onUpdateCollection);
    on<AddFolderToCollectionDetail>(_onAddFolderToCollectionDetail);
  }

  Future<void> _onFetchCollections(FetchCollections event, Emitter<CollectionState> emit) async {
    print('FetchCollections called...');
    emit(CollectionLoading());
    final connectivity = await Connectivity().checkConnectivity();
    if (connectivity.contains(ConnectivityResult.none)) {
      emit(const CollectionError('No internet connection'));
      return;
    }
    try {
      final apiResponse = await repository.fetchCollections(event.workspaceId);
      if (apiResponse.status == 'Success') {
        emit(CollectionLoaded(apiResponse.getCollections()));
      }else {
        emit(CollectionError(apiResponse.msg ?? 'Failed to load collections'));
      }
    } catch (e) {
      emit(CollectionError(e.toString()));
    }
  }

  Future<void> _onFetchCollectionDetail(FetchCollectionDetail event,  Emitter<CollectionState> emit) async {
    emit(CollectionDetailLoading());

    final connectivity = await Connectivity().checkConnectivity();
    if (connectivity.contains(ConnectivityResult.none)) {
      emit(const CollectionDetailError('No internet connection'));
      return;
    }

    try {
      final apiResponse = await repository.fetchCollectionDetails(
        event.collectionId,
        event.workspaceId
      );

      if (apiResponse.status == 'Success') {
        emit(CollectionDetailLoaded(apiResponse.data));
      } else {
        emit(CollectionDetailError(apiResponse.msg ?? 'Failed to load collection details'));
      }
    } catch (e) {
      emit(CollectionDetailError(e.toString()));
    }
  }

  Future<void> _onCreateCollection(CreateCollection event, Emitter<CollectionState> emit) async {
    emit(CollectionCreating());

    final connectivity = await Connectivity().checkConnectivity();
    if (connectivity.contains(ConnectivityResult.none)) {
      emit(const CollectionCreationError('No internet connection'));
      return;
    }

    try {
      final apiResponse = await repository.createCollection(
        title: event.title,
        description: event.description,
        workspaceId: event.workspaceId,
      );

      if (apiResponse.status == 'Success' && apiResponse.data != null) {
        // Get the newly created collection from the response
        Collection? newCollection = apiResponse.getSingleCollection();

        if (newCollection != null) {
          // Emit the success state with the new collection
          emit(CollectionCreated('Collection created successfully', newCollection));

          // We don't need to refresh the collections list anymore as we'll update the UI directly
          // with the new collection
        } else {
          emit(const CollectionCreationError('Failed to parse collection data'));
        }
      } else {
        emit(CollectionCreationError(apiResponse.msg ?? 'Failed to create collection'));
      }
    } catch (e) {
      emit(CollectionCreationError(e.toString()));
    }
  }

  Future<void> _onDeleteCollection(DeleteCollection event, Emitter<CollectionState> emit) async {
    emit(CollectionDeleting());

    final connectivity = await Connectivity().checkConnectivity();
    if (connectivity.contains(ConnectivityResult.none)) {
      emit(const CollectionDeletionError('No internet connection'));
      return;
    }

    try {
      final apiResponse = await repository.deleteCollection(event.collectionId);

      if (apiResponse.status == 'Success') {
        // Emit the success state with the deleted collection ID
        emit(CollectionDeleted('Collection deleted successfully', event.collectionId));
      } else {
        emit(CollectionDeletionError(apiResponse.msg ?? 'Failed to delete collection'));
      }
    } catch (e) {
      emit(CollectionDeletionError(e.toString()));
    }
  }

  Future<void> _onUpdateCollection(UpdateCollection event, Emitter<CollectionState> emit) async {
    emit(CollectionUpdating());

    final connectivity = await Connectivity().checkConnectivity();
    if (connectivity.contains(ConnectivityResult.none)) {
      emit(const CollectionUpdateError('No internet connection'));
      return;
    }

    try {
      final apiResponse = await repository.updateCollection(
        collectionId: event.collectionId,
        title: event.title,
        description: event.description,
        workspaceId: event.workspaceId,
      );

      if (apiResponse.status == 'Success' && apiResponse.data != null) {
        // Get the updated collection from the response
        Collection? updatedCollection = apiResponse.getSingleCollection();

        if (updatedCollection != null) {
          // Emit the success state with the updated collection
          emit(CollectionUpdated('Collection updated successfully', updatedCollection));
        } else {
          emit(const CollectionUpdateError('Failed to parse collection data'));
        }
      } else {
        emit(CollectionUpdateError(apiResponse.msg ?? 'Failed to update collection'));
      }
    } catch (e) {
      emit(CollectionUpdateError(e.toString()));
    }
  }

  void _onAddFolderToCollectionDetail(AddFolderToCollectionDetail event, Emitter<CollectionState> emit) {
    // Only update if we currently have a CollectionDetailLoaded state for the same collection
    if (state is CollectionDetailLoaded) {
      final currentState = state as CollectionDetailLoaded;
      final currentDetail = currentState.collectionDetail;

      if (currentDetail != null && currentDetail.id == event.collectionId) {
        List<Folder> updatedFolders;

        // Check if this is a subfolder (has parentFolderId)
        if (event.newFolder.parentFolderId != null) {
          // This is a subfolder, find the parent folder and add to its folders list
          updatedFolders = _addFolderToParent(currentDetail.folders ?? [], event.newFolder);
          print('Added subfolder "${event.newFolder.name}" to parent folder ID: ${event.newFolder.parentFolderId}');
        } else {
          // This is a root-level folder, add to collection's folders list
          final currentFolders = currentDetail.folders ?? [];
          updatedFolders = [...currentFolders, event.newFolder];
          print('Added root folder "${event.newFolder.name}" to collection. Total root folders: ${updatedFolders.length}');
        }

        final updatedDetail = CollectionDetail(
          id: currentDetail.id,
          name: currentDetail.name,
          description: currentDetail.description,
          userId: currentDetail.userId,
          workspaceId: currentDetail.workspaceId,
          isActive: currentDetail.isActive,
          folders: updatedFolders,
          requests: currentDetail.requests,
          createdAt: currentDetail.createdAt,
          updatedAt: currentDetail.updatedAt,
        );

        // Emit the updated state
        emit(CollectionDetailLoaded(updatedDetail));
      }
    }
  }

  // Helper method to recursively find parent folder and add new folder to it
  List<Folder> _addFolderToParent(List<Folder> folders, Folder newFolder) {
    return folders.map((folder) {
      if (folder.id == newFolder.parentFolderId) {
        // Found the parent folder, add the new folder to its folders list
        final currentSubfolders = folder.folders ?? [];
        final updatedSubfolders = [...currentSubfolders, newFolder];
        return Folder(
          id: folder.id,
          name: folder.name,
          description: folder.description,
          collectionId: folder.collectionId,
          parentFolderId: folder.parentFolderId,
          order: folder.order,
          isActive: folder.isActive,
          createdAt: folder.createdAt,
          updatedAt: folder.updatedAt,
          folders: updatedSubfolders,
          requests: folder.requests,
        );
      } else if (folder.folders != null && folder.folders!.isNotEmpty) {
        // Recursively search in subfolders
        final updatedSubfolders = _addFolderToParent(folder.folders!, newFolder);
        return Folder(
          id: folder.id,
          name: folder.name,
          description: folder.description,
          collectionId: folder.collectionId,
          parentFolderId: folder.parentFolderId,
          order: folder.order,
          isActive: folder.isActive,
          createdAt: folder.createdAt,
          updatedAt: folder.updatedAt,
          folders: updatedSubfolders,
          requests: folder.requests,
        );
      }
      return folder;
    }).toList();
  }
}


