
import 'dart:convert';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_code_editor/flutter_code_editor.dart';
import 'package:flutter_highlight/themes/a11y-dark.dart';
import 'package:postman_flutter/features/home/<USER>/postmanrequest/postman_request_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/postmanrequest/postman_request_state.dart';

import '../../../helpers/color_config.dart';
import '../../../helpers/style_config.dart';
import '../../common_widgets/CommonText.dart';
import '../../common_widgets/dynamic_tabs_widget.dart';
import 'package:highlight/languages/json.dart' show json;

import '../bloc/home_bloc.dart';
import '../bloc/home_event.dart';
import '../bloc/home_state.dart';
import 'dynamic_large_response_view.dart';
import 'dynamic_json_view.dart';

class ResponseView extends StatefulWidget {
  final String uuid;

  const ResponseView({required this.uuid, super.key});

  @override
  State<ResponseView> createState() => _ResponseViewState();
}

class _ResponseViewState extends State<ResponseView> with AutomaticKeepAliveClientMixin {
  late final TextEditingController _responseController;
  late final CodeController _codeController;
  late final TextEditingController _searchController;
  Map<String, dynamic>? _responseData;
  dynamic _responseBodyData;
  Map<String, dynamic>? _responseHeadersData;
  bool _isLargeResponse = false; 
  bool _isSearchVisible = false;
  String _searchQuery = '';
  List<int> _searchMatches = [];
  int _currentMatchIndex = -1;
  String _fullResponseText = '';
  Timer? _searchDebounceTimer;
  int _scrollTrigger = 0; 
  int _copyTrigger = 0; 
  @override
  bool get wantKeepAlive => true; // Keep this widget alive when it's not visible

  @override
  void initState() {
    super.initState();
    _responseController = TextEditingController();
    _searchController = TextEditingController();
    _codeController = CodeController(
      text: _responseController.text,
      language: json,
    );
    _responseController.addListener(() {
      if (_responseController.text != _codeController.text) {
        _codeController.text = _responseController.text;
        _fullResponseText = _responseController.text;
        if (mounted) {
          setState(() {});
        }
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateResponseController();
  }

  void _extractResponseData(dynamic responseJsonData) {
    _responseData = responseJsonData is Map<String, dynamic> ? responseJsonData : null;
    if (responseJsonData is Map<String, dynamic>) {
      if (responseJsonData.containsKey('response')) {
        final response = responseJsonData['response'];

        if (response is Map<String, dynamic>) {
          if (response.containsKey('body')) {
            _responseBodyData = response['body'];
          } else {
            _responseBodyData = null;
          }

          if (response.containsKey('headers')) {
            _responseHeadersData = response['headers'] as Map<String, dynamic>;
          } else {
            _responseHeadersData = null;
          }
        } else {
          _responseBodyData = response; 
          _responseHeadersData = null;
        }
      } else {
        if (responseJsonData.containsKey('body')) {
          _responseBodyData = responseJsonData['body'];
        } else {
          _responseBodyData = responseJsonData;
        }

        if (responseJsonData.containsKey('headers')) {
          _responseHeadersData = responseJsonData['headers'] as Map<String, dynamic>;
        } else {
          _responseHeadersData = null;
        }
      }
    } else if (responseJsonData is List) {
      _responseBodyData = responseJsonData;
      _responseHeadersData = null;
    } else {
      _responseBodyData = null;
      _responseHeadersData = null;
    }
  }

  bool _checkIsLargeResponse(dynamic data) {
    try {
      final jsonString = jsonEncode(data);
      return jsonString.length > 10 * 1024; // 10KB threshold
    } catch (e) {
      return false;
    }
  }

  Future<String> _processJsonInIsolate(dynamic data) async {
    try {
      final completer = Completer<String>();

      Future.microtask(() {
        try {
          final prettyJson = const JsonEncoder.withIndent('  ').convert(data);
          completer.complete(prettyJson);
        } catch (e) {
          completer.complete("Error formatting JSON: $e");
        }
      });

      return await completer.future;
    } catch (e) {
      return "Error processing JSON: $e";
    }
  }

  void _updateResponseController() {
    final activeTab = context.read<HomeBloc>().state.activeTab;
    _clearSearch();

    if (activeTab != null && activeTab.uuid == widget.uuid) {
      final responseJsonData = activeTab.responseJsonData;

      if (responseJsonData != null) {
        _responseController.text = "Processing response data...";
        _codeController.text = "Processing response data...";

        _extractResponseData(responseJsonData);
        final isLarge = _checkIsLargeResponse(responseJsonData);

        if (isLarge) {
          _responseController.text = "Response is large. Using optimized viewer.";
          _codeController.text = "Response is large. Using optimized viewer.";
          _processJsonInIsolate(responseJsonData).then((prettyJson) {
            if (mounted) {
              _fullResponseText = prettyJson;
            }
          }).catchError((e) {
            if (mounted) {
              _fullResponseText = "Error formatting response: ${e.toString()}";
            }
          });
          setState(() {
            _isLargeResponse = true;
          });
        } else {
          _processJsonInIsolate(responseJsonData).then((prettyJson) {
            if (mounted) {
              _responseController.text = prettyJson;
              _codeController.text = prettyJson;
              _fullResponseText = prettyJson;
              setState(() {
                _isLargeResponse = false;
              });
            }
          }).catchError((e) {
            if (mounted) {
              final errorMsg = "Error formatting response: ${e.toString()}";
              _responseController.text = errorMsg;
              _codeController.text = errorMsg;
              _fullResponseText = errorMsg;
              setState(() {});
            }
          });
        }
      } else {
        _responseController.text = "";
        _codeController.text = "";
        _fullResponseText = "";
        _responseData = null;
        _responseBodyData = null;
        _responseHeadersData = null;
        setState(() {
          _isLargeResponse = false;
        });
      }
    } else {
      _responseController.text = "No response data available.";
      _codeController.text = "No response data available.";
      _fullResponseText = "No response data available.";
      _responseData = null;
      _responseBodyData = null;
      _responseHeadersData = null;
      setState(() {
        _isLargeResponse = false;
      });
    }
  }

  @override
  void didUpdateWidget(ResponseView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.uuid != widget.uuid) {
      _updateResponseController();
    }
  }

  void _performSearch(String query) {
    debugPrint('Performing search for: "$query"');
    debugPrint('Is large response: $_isLargeResponse');
    debugPrint('Response body data: ${_responseBodyData != null}');

    if (query.isEmpty) {
      setState(() {
        _searchQuery = '';
        _searchMatches.clear();
        _currentMatchIndex = -1;
      });
      return;
    }

    setState(() {
      _searchQuery = query.toLowerCase();
      // Clear previous search results - let the components handle the new search
      _searchMatches.clear();
      _currentMatchIndex = -1;
    });

    // Delegate search to the appropriate component based on response type
    if (_responseBodyData != null) {
      if (_isLargeResponse) {
        // For large responses, let DynamicLargeResponseView handle the search
        _delegateSearchToLargeResponseView(query);
      } else {
        // For normal responses, let DynamicJsonView handle the search
        _delegateSearchToJsonView(query);
      }
    }
  }

  void _delegateSearchToJsonView(String query) {
    debugPrint('Delegating search to DynamicJsonView for query: "$query"');
    // The search will be handled by DynamicJsonView through the searchQuery parameter
    // The component will receive the searchQuery and perform its own search
    // We just need to trigger a rebuild to pass the new search query
    setState(() {
      // The search query is already set above, just trigger rebuild
      // DynamicJsonView will handle the search internally and update search results
    });
  }

  void _delegateSearchToLargeResponseView(String query) {
    debugPrint('Delegating search to DynamicLargeResponseView for query: "$query"');
    // The search will be handled by DynamicLargeResponseView through the searchQuery parameter
    // The component will receive the searchQuery and perform its own search
    // We just need to trigger a rebuild to pass the new search query
    setState(() {
      // The search query is already set above, just trigger rebuild
      // DynamicLargeResponseView will handle the search internally and update search results
    });
  }

  void _onSearchResultsChanged(List<int> matches, int currentIndex) {
    debugPrint('Search results received: ${matches.length} matches, current index: $currentIndex');

    // Use post-frame callback to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _searchMatches = matches;
          _currentMatchIndex = currentIndex;
        });
      }
    });
  }

  void _nextMatch() {
    if (_searchMatches.isNotEmpty) {
      final newIndex = (_currentMatchIndex + 1) % _searchMatches.length;
      debugPrint('Navigated to next match: ${newIndex + 1} of ${_searchMatches.length}');
      setState(() {
        _currentMatchIndex = newIndex;
        _scrollTrigger++;
      });
      debugPrint('ResponseView: Updated scroll trigger to $_scrollTrigger for match index $newIndex');
    }
  }

  void _previousMatch() {
    if (_searchMatches.isNotEmpty) {
      final newIndex = _currentMatchIndex > 0
          ? _currentMatchIndex - 1
          : _searchMatches.length - 1;
      debugPrint('Navigated to previous match: ${newIndex + 1} of ${_searchMatches.length}');

      setState(() {
        _currentMatchIndex = newIndex;
        _scrollTrigger++;
      });
      debugPrint('ResponseView: Updated scroll trigger to $_scrollTrigger for match index $newIndex');
    }
  }



  void _clearSearch() {
    _searchController.clear();
    _searchMatches.clear();
    _currentMatchIndex = -1;
    _searchQuery = '';
  }

  void _toggleSearch() {
    setState(() {
      _isSearchVisible = !_isSearchVisible;
      if (!_isSearchVisible) {
        _clearSearch();
      }
    });
  }

  Future<void> _copyToClipboard() async {
    debugPrint('Copy button pressed - isLargeResponse: $_isLargeResponse');
    if (_responseBodyData != null) {
      if (_isLargeResponse) {
        _copyFromLargeResponseView();
      } else {
        _copyFromJsonView();
      }
    } else {
      if (_fullResponseText.isNotEmpty) {
        await Clipboard.setData(ClipboardData(text: _fullResponseText));
        _showCopySuccessSnackbar();
      }
    }
  }

  void _copyFromJsonView() {
    debugPrint('Delegating copy to DynamicJsonView');
    setState(() {
      _copyTrigger++;
    });
  }

  void _copyFromLargeResponseView() {
    debugPrint('Delegating copy to DynamicLargeResponseView');
    setState(() {
      _copyTrigger++;
    });
  }

  void _showCopySuccessSnackbar() {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Response copied to clipboard'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  @override
  void dispose() {
    _searchDebounceTimer?.cancel();
    _responseController.dispose();
    _searchController.dispose();
    _codeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return BlocListener<PostmanRequestBloc, PostmanRequestState>(
      listener: (context, state) {
        if (state is DirectRequestSent && state.tabUuid == widget.uuid) {
          _clearSearch();

          if (state.response['status'] == 'success' && state.response['data'] != null) {
            context.read<HomeBloc>().add(
              UpdateResponseJsonDataEventTab(state.response['data']),
            );

            _extractResponseData(state.response['data']);

            final isLarge = _checkIsLargeResponse(state.response['data']);

            if (isLarge) {
              _responseController.text = "Response is large. Using optimized viewer.";
              _codeController.text = "Response is large. Using optimized viewer.";
              _processJsonInIsolate(state.response['data']).then((prettyJson) {
                if (mounted) {
                  _fullResponseText = prettyJson;
                }
              }).catchError((e) {
                if (mounted) {
                  _fullResponseText = "Error formatting response: ${e.toString()}";
                }
              });

              setState(() {
                _isLargeResponse = true;
              });
            } else {
              _processJsonInIsolate(state.response['data']).then((prettyJson) {
                if (mounted) {
                  _responseController.text = prettyJson;
                  _codeController.text = prettyJson;
                  _fullResponseText = prettyJson;
                  setState(() {
                    _isLargeResponse = false;
                  });
                }
              }).catchError((e) {
                if (mounted) {
                  final errorMsg = "Error formatting response: ${e.toString()}";
                  _responseController.text = errorMsg;
                  _codeController.text = errorMsg;
                  _fullResponseText = errorMsg;
                  setState(() {
                    _isLargeResponse = false;
                  });
                }
              });
            }
          } else {
            _responseController.text = "No data in response or invalid response format";
            _codeController.text = "No data in response or invalid response format";
            _fullResponseText = "No data in response or invalid response format";
            _responseData = null;
            _responseBodyData = null;
            _responseHeadersData = null;

            setState(() {
              _isLargeResponse = false;
            });
          }
        } else if (state is DirectRequestError && state.tabUuid == widget.uuid) {
          _responseController.text = "Error: ${state.message}";
          _codeController.text = "Error: ${state.message}";
          _fullResponseText = "Error: ${state.message}";
          _responseData = null;
          _responseBodyData = null;
          _responseHeadersData = null;

          setState(() {
            _isLargeResponse = false;
          });
        } else if (state is SendingDirectRequest) {
          _responseController.text = "Sending request...";
          _codeController.text = "Sending request...";
          _fullResponseText = "Sending request...";
        }
      },
      child: BlocBuilder<HomeBloc, HomeState>(
        buildWhen: (previous, current) {
          final previousActiveTab = previous.activeTab;
          final currentActiveTab = current.activeTab;

          if (previousActiveTab == currentActiveTab) {
            return false;
          }

          if (previousActiveTab?.uuid != currentActiveTab?.uuid) {
            return previousActiveTab?.uuid == widget.uuid || currentActiveTab?.uuid == widget.uuid;
          }
          if (currentActiveTab?.uuid == widget.uuid) {
            return previousActiveTab?.responseJsonData != currentActiveTab?.responseJsonData;
          }

          return false;
        },
        builder: (context, homeState) {
          final activeTab = homeState.activeTab;
          if (homeState.openTabs.isEmpty) {
            _responseController.text = "No response data available.";
            _fullResponseText = "No response data available.";
          }

          if (activeTab != null && activeTab.uuid == widget.uuid && activeTab.responseJsonData != null) {
            Future.microtask(() {
              if (mounted) {
                if (_responseData == null ||
                    (_responseBodyData == null && _responseHeadersData == null) ||
                    _responseData != activeTab.responseJsonData) {
                  _updateResponseController();
                }
              }
            });
          }

          return _buildResponseView();
        },
      ),
    );
  }

  Widget _buildResponseView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 0.0, bottom: 4),
          child: CommonText(
            text: "Response",
            textStyle: mMediumTextStyle16(
              textSize: 14,
              textColor: AppThemeColor.white,
            ),
          ),
        ),
        Expanded(
          child: DynamicTabsWidget(
            tabTitles: const ['Body', 'Headers'],
            tabViews: [
              _buildBodyTab(),
              _buildHeadersTab(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBodyTab() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 2.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              if (_responseBodyData != null) ...[
                // Copy button
                IconButton(
                  icon: const Icon(
                    Icons.copy,
                    size: 16,
                    color: Colors.white70,
                  ),
                  onPressed: _copyToClipboard,
                  //tooltip: 'Copy response',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                ),
                // Search button
                IconButton(
                  icon: const Icon(
                    Icons.search,
                    size: 16,
                    color: Colors.white70,
                  ),
                  onPressed: _toggleSearch,
                  //tooltip: 'Search in response',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                ),
              ],
            ],
          ),
        ),


        Expanded(
          child: Stack(
            children: [
              _buildResponseContent(),
              if (_isSearchVisible)
                Positioned(
                  top: 8,
                  right: 8,
                  child: _buildFloatingSearchOverlay(),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFloatingSearchOverlay() {
    return Focus(
      onKeyEvent: (node, event) {
        if (event is KeyDownEvent) {
          if (event.logicalKey == LogicalKeyboardKey.escape) {
            _toggleSearch();
            return KeyEventResult.handled;
          } else if (event.logicalKey == LogicalKeyboardKey.enter) {
            if (_searchMatches.isNotEmpty) {
              _nextMatch();
            }
            return KeyEventResult.handled;
          } else if (event.logicalKey == LogicalKeyboardKey.f3) {
            if (_searchMatches.isNotEmpty) {
              _nextMatch();
            }
            return KeyEventResult.handled;
          }
        }
        return KeyEventResult.ignored;
      },
      child: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(6),
        color: Colors.white,
      child: Container(
        width: 320,
        height: 40,
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(color: Colors.grey.shade300, width: 1),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: TextField(
                controller: _searchController,
                autofocus: true,
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                ),
                decoration: const InputDecoration(
                  hintText: 'Search...',
                  hintStyle: TextStyle(color: Colors.grey, fontSize: 14),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 4, vertical: 8),
                  isDense: true,
                ),
                onSubmitted: (value) {
                  if (_searchMatches.isNotEmpty) {
                    _nextMatch();
                  }
                },
                onChanged: (value) {
                  _searchDebounceTimer?.cancel();
                  _searchDebounceTimer = Timer(const Duration(milliseconds: 300), () {
                    if (mounted) {
                      _performSearch(value);
                    }
                  });
                },
              ),
            ),

            if (_searchMatches.isNotEmpty) ...[
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(3),
                ),
                child: Text(
                  '${_currentMatchIndex + 1} of ${_searchMatches.length}',
                  style: const TextStyle(
                    color: Colors.black87,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(width: 4),
            ],

            _buildSearchNavButton(
              icon: Icons.keyboard_arrow_up,
              onPressed: _searchMatches.isNotEmpty ? _previousMatch : null,
              tooltip: 'Previous match',
            ),
            _buildSearchNavButton(
              icon: Icons.keyboard_arrow_down,
              onPressed: _searchMatches.isNotEmpty ? _nextMatch : null,
              tooltip: 'Next match',
            ),

            const SizedBox(width: 2),

            _buildSearchNavButton(
              icon: Icons.close,
              onPressed: _toggleSearch,
              tooltip: 'Close search',
            ),
          ],
        ),
      ),
    ),
    );
  }

  Widget _buildSearchNavButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required String tooltip,
  }) {
    return Tooltip(
      message: tooltip,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(4),
        child: Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: onPressed != null
                ? Colors.transparent
                : Colors.grey.shade100,
          ),
          child: Icon(
            icon,
            size: 16,
            color: onPressed != null
                ? Colors.grey.shade700
                : Colors.grey.shade400,
          ),
        ),
      ),
    );
  }

  Widget _buildResponseContent() {
    if (_responseBodyData != null) {
      if (_isLargeResponse) {
        return DynamicLargeResponseView(
          jsonData: _responseBodyData,
          isDarkMode: true,
          searchQuery: _searchQuery.isNotEmpty ? _searchQuery : null,
          searchMatches: _searchMatches,
          currentMatchIndex: _currentMatchIndex >= 0 ? _currentMatchIndex : null,
          onSearchResultsChanged: _onSearchResultsChanged,
          scrollTrigger: _scrollTrigger,
          copyTrigger: _copyTrigger,
          onCopyCompleted: _showCopySuccessSnackbar,
          key: ValueKey(_responseBodyData.hashCode),
        );
      } else {
        return DynamicJsonView(
          jsonData: _responseBodyData,
          isDarkMode: true,
          searchQuery: _searchQuery.isNotEmpty ? _searchQuery : null,
          searchMatches: _searchMatches,
          currentMatchIndex: _currentMatchIndex >= 0 ? _currentMatchIndex : null,
          onSearchResultsChanged: _onSearchResultsChanged,
          scrollTrigger: _scrollTrigger,
          copyTrigger: _copyTrigger,
          onCopyCompleted: _showCopySuccessSnackbar,
          key: ValueKey(_responseBodyData.hashCode),
        );
      }
    }
    else if (_responseData != null) {
      return CodeTheme(
        data: CodeThemeData(styles: a11yDarkTheme),
        child: CodeField(
          padding: const EdgeInsets.only(top: 23),
          background: AppThemeColor.commonBackground,
          controller: _codeController,
          readOnly: true,
          expands: true,
          wrap: true,
          textStyle: mRegularTextStyle16(textSize: 12),
        ),
      );
    }
    else {
      return Center(
        child: CommonText(
          text: "No response body data available",
          textStyle: mMediumTextStyle16(
            textSize: 14,
            textColor: AppThemeColor.white,
          ),
        ),
      );
    }
  }

  Widget _buildHeadersTab() {
    if (_responseHeadersData == null) {
      return Center(
        child: CommonText(
          text: "No headers data available",
          textStyle: mMediumTextStyle16(
            textSize: 14,
            textColor: AppThemeColor.white,
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: ListView(
        children: _responseHeadersData!.entries.map((entry) {
          return Container(
            padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 12.0),
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: AppThemeColor.dividerBackgroundColor,
                  width: 1.0,
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: CommonText(
                    text: entry.key,
                    textStyle: mMediumTextStyle16(
                      textSize: 13,
                      textColor: Colors.blue,
                    ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: CommonText(
                    text: entry.value.toString(),
                    textStyle: mRegularTextStyle16(
                      textSize: 13,
                      textColor: AppThemeColor.white,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }
}