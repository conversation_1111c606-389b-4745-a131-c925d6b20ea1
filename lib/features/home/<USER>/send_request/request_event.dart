// Define event to trigger request
abstract class RequestEvent {}

class SendRequestEvent extends RequestEvent {
  final String method;
  final String url;
  final Map<String, String>? headers;
  final Map<String, dynamic>? body;
  final String tabUuid; // Add tab UUID to associate response with specific tab

  SendRequestEvent({
    required this.method,
    required this.url,
    this.headers,
    this.body,
    required this.tabUuid,
  });

}


class SendRequestEventFormData extends RequestEvent {
  final String method;
  final String url;
  final Map<String, String>? headers;
  final Map<String, String>? formData;
  final String tabUuid; // Add tab UUID to associate response with specific tab

  SendRequestEventFormData({
    required this.method,
    required this.url,
    this.headers,
    this.formData,
    required this.tabUuid,
   }
  );
}


class SendRequestEventQueryParams extends RequestEvent {
  final String method;
  final String url;
  final Map<String, String>? headers;
  final Map<String, dynamic>? body;
  final Map<String, String>? formData;
  final String tabUuid; // Add tab UUID to associate response with specific tab

  SendRequestEventQueryParams({
    required this.method,
    required this.url,
    this.headers,
    this.body,
    this.formData,
    required this.tabUuid,
  });
}



