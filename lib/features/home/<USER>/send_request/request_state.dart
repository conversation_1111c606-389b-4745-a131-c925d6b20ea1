// Define possible states
abstract class RequestState {}

class RequestInitialState extends RequestState {}

class RequestLoadingState extends RequestState {}

class RequestSuccessState extends RequestState {
  final dynamic response;
  final String tabUuid; // Add tab UUID to associate response with specific tab

  RequestSuccessState(this.response, this.tabUuid);
}

class RequestErrorState extends RequestState {
  final String error;

  RequestErrorState(this.error);
}
