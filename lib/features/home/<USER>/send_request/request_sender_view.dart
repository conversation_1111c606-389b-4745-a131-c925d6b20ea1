import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_code_editor/flutter_code_editor.dart';
import 'package:postman_flutter/features/home/<USER>/home_state.dart';
import 'package:postman_flutter/features/home/<USER>';
import 'package:postman_flutter/features/home/<USER>/send_request/request_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/send_request/request_event.dart';
import '../../../../helpers/color_config.dart';
import '../../../../helpers/style_config.dart';
import '../../../common_widgets/CommonButton.dart';
import '../../../common_widgets/common_dropdown.dart';
import 'package:http/http.dart' as http;
import '../../../common_widgets/dynamic_radio_tabs/bloc/tab_bloc.dart';
import '../../../common_widgets/dynamic_radio_tabs/bloc/tab_state.dart';
import '../../bloc/home_bloc.dart';
import '../../bloc/home_event.dart';
import '../../bloc/postmanrequest/postman_request_bloc.dart';
import '../../bloc/postmanrequest/postman_request_event.dart';
import '../../bloc/postmanrequest/postman_request_state.dart';
import '../authorization/basic_auth/basic_auth.dart';
import '../authorization/bloc/auth_subviews_bloc.dart';
import '../authorization/bloc/auth_subviews_state.dart';
import '../authorization/bloc/authorization_bloc.dart';
import '../authorization/bloc/authorization_state.dart';
import '../body/form_data/bloc/form_data_bloc.dart';
import '../body/form_data/bloc/form_data_model.dart';
import '../body/json_data/bloc/json_data_bloc.dart';
import '../header/bloc/headers_bloc.dart';
import '../header/bloc/headers_model.dart';
import '../query_params/bloc/query_params_bloc.dart';
import '../query_params/bloc/query_params_event.dart';
import '../query_params/bloc/query_params_state.dart';
import 'request_bloc.dart'; 


class RequestSenderView extends StatefulWidget {
  final String uuid;

  const RequestSenderView({
    required this.uuid,
    Key? key,
  }) : super(key: key);

  @override
  State<RequestSenderView> createState() => _RequestSenderViewState();
}

class _RequestSenderViewState extends State<RequestSenderView> {
  late TextEditingController _urlController;
  late String _selectedMethod;

  @override
  void initState() {
    super.initState();
    _urlController = TextEditingController();
    _selectedMethod = "GET";
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateControllersFromActiveTab();
  }

  void _updateControllersFromActiveTab() {
    final activeTab = context.read<HomeBloc>().state.activeTab;
    if (activeTab != null && activeTab.uuid == widget.uuid) {
      _urlController.text = activeTab.url;
      _selectedMethod = activeTab.method;
    } else{
      _urlController.text = "";
      _selectedMethod = "GET";
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<AuthSubViewsBloc, AuthSubViewsState>(
          listener: (context, state) {
            //getting data from basic auth
            if (state.authType == "Basic Auth") {
              // Optionally show feedback or log the updated values
              debugPrint("UsernameRS: ${state.basicAuthFields.username}");
              debugPrint("PasswordRS: ${state.basicAuthFields.password}");
            }

            //getting data from bearer token
            if (state.authType == "Bearer Token") {
              // Optionally show feedback or log the updated values
              debugPrint("TokenRS: ${state.bearerTokenFields.token}");
            }

            debugPrint("AuthType: ${state.authType}");

            debugPrint("HeaderOrQueryParamsApiKey: ${state.apiKeyFields.addToHeaderOrQueryParams}");

            //getting data from api key
            if (state.authType == "API Key") {
              // Optionally show feedback or log the updated values
              debugPrint("ApiKey: ${state.apiKeyFields.key}");
              debugPrint("ApiValue: ${state.apiKeyFields.value}");
              debugPrint("ApiHeaderOrQueryParams: ${state.apiKeyFields.addToHeaderOrQueryParams}");
            }
            //getting data from jwt bearer
            debugPrint("algorithm: ${state.jwtBearerFields.algorithm}");
            debugPrint("secretToken: ${state.jwtBearerFields.secretToken}");
            debugPrint("payload: ${state.jwtBearerFields.payload}");
            debugPrint("requestHeaderprefix: ${state.jwtBearerFields.requestHeaderprefix}");
            debugPrint("jwtHeader: ${state.jwtBearerFields.jwtHeader}");

            debugPrint("jwtTokenToHeaderOrQueryParams: ${state.jwtBearerFields.jwtTokenToHeaderOrQueryParams}");

            debugPrint('AuthSubViewsBloc Listener');
          },
        ),
        BlocListener<QueryParamsBloc, QueryParamsState>(
          listener: (context, state) {
            final baseUrl = _urlController.text.split('?').first;
            final queryString = state.generateQueryString();
            final newUrl = queryString.isNotEmpty ? '$baseUrl$queryString' : baseUrl;

            if (_urlController.text != newUrl) {
              _urlController.text = newUrl;
            }
            debugPrint('QueryParams Listener:');
          },
        ),
      ],
      child: BlocBuilder<HomeBloc, HomeState>(
        builder: (context, state) {
          _urlController.text = state.activeTab?.url??"";
          return Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Container(
                    height: 40,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: AppThemeColor.commonBorderColor,
                        width: 1.0,
                      ),
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        const SizedBox(width: 8.0),
                        SizedBox(
                          width: 100,
                          child: CommonDropdown(
                            key: ValueKey(widget.uuid),
                            initialText: state.activeTab?.method,
                            items: const ["GET", "POST", "PUT", "DELETE", "PATCH"],
                            backgroundColor: AppThemeColor.commonBackground,
                            dropdownBackgroundColor: Colors.grey[800]!,
                            textStyle: const TextStyle(color: Colors.white),
                            textPadding: const EdgeInsets.only(right: 4, left: 15),
                            iconPadding: const EdgeInsets.only(left: 4),
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            onChanged: (value) {
                              context.read<HomeBloc>().add(
                                ChangeMethodEvent(
                                  uuid: widget.uuid,
                                  method: value,
                                ),
                              );
                              setState(() {
                                _selectedMethod = value;
                              });
                            },
                          ),
                        ),
                        const VerticalDivider(
                          color: AppThemeColor.commonDropdownArrowColor,
                          thickness: 1,
                          indent: 5,
                          endIndent: 5,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: TextField(
                            controller: _urlController..selection = TextSelection.fromPosition(TextPosition(offset: (state.activeTab?.url??'').length)),
                            style: const TextStyle(color: Colors.white, fontSize: 12,),
                            decoration: const InputDecoration(
                                contentPadding: EdgeInsets.zero,
                                hintText: 'https://example.com/services',
                                hintStyle: TextStyle(
                                    color: Colors.grey, fontSize: 12,
                                    fontFamily: 'inter',
                                    fontWeight: FontWeight.w400),
                                border: InputBorder.none,
                                isDense: true,
                            ),
                            onChanged: (value) {
                              context.read<HomeBloc>().add(
                                ChangeUrlEvent(
                                  uuid: widget.uuid,
                                  url: value,
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 8.0),
                BlocBuilder<TabBloc, TabState>(
                  builder: (context, state) {
                    final selectedTabIndex = state is TabUpdated ? state.tabIndex : 0;
                    print("Selected Tab Index: $selectedTabIndex");
                    return CommonButton(
                      buttonText: "Send",
                      height: 40,
                      width: 100,
                      backgroundColor: AppThemeColor.buttonBlue,
                      textStyle: mRegularTextStyle16(
                        textSize: 12,
                        textColor: AppThemeColor.white,
                      ),
                      callback: () {
                        if (selectedTabIndex == 0) {
                          sendRequestFromNone(context);
                        } else if (selectedTabIndex == 1) {
                          debugPrint("Send request with form data");
                          sendRequestFromFormData(context);
                        } else if (selectedTabIndex == 2) {
                          debugPrint("Send request with JSON");
                          sendRequestFromBodyJson(context);
                        } else{
                          sendRequestFromNone(context);
                        }
                      }
                    );
                  },
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Map<String, String> combineHeaders(BuildContext context) {
    final headersBloc = context.read<HeadersBloc>();
    final authBloc = context.read<AuthSubViewsBloc>();
    final authState = authBloc.state;

    final headers = <String, String>{};

    // Combine headers from HeadersBloc
    for (var row in headersBloc.state.rows) {
      if (row.isSelected && row.key.isNotEmpty && row.value.isNotEmpty) {
        headers[row.key] = row.value;
      }
    }
    //print('header --> $headers');

    // Add authorization headers based on the selected auth type
    // if (authState is AuthorizationLoaded) {
    //   if (authState.authType == "Basic Auth") {
    //     headers['Authorization'] = 'Basic ${base64Encode(utf8.encode('${authState.basicAuthFields.username}:${authState.basicAuthFields.password}'))}';
    //   } else if (authState.authType == "Bearer Token") {
    //     headers['Authorization'] = 'Bearer ${authState.bearerTokenFields.token}';
    //   } else if (authState.authType == "API Key") {
    //     headers[authState.apiKeyFields.key] = authState.apiKeyFields.value;
    //   } else if (authState.authType == "JWT Bearer") {
    //     headers[authState.jwtBearerFields.jwtHeader] = 'Bearer ${authState.jwtBearerFields.secretToken}';
    //   }
    // }

    return headers;
  }

  void sendRequestFromBodyJson(BuildContext context) {
    final state = context.read<JsonDataBloc>().state;
    final headers = combineHeaders(context);

    _sendRequestBody(body: state.jsonData, headers: headers);
  }

  void _sendRequestBody({required Map<String, dynamic>? body, required Map<String, String> headers}) {
    final url = _urlController.text.trim();
    if (url.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Please enter a valid URL.')));
      return;
    }

    // Show loading indicator
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Sending request...'),
        duration: Duration(seconds: 1),
      ),
    );
    final bodyData = {
      "type": "json",
      "content": jsonEncode(body)
    };

    context.read<PostmanRequestBloc>().add(SendDirectRequest(
      method: _selectedMethod,
      url: url,
      headers: headers,
      body: bodyData,
      tabUuid: widget.uuid,
    ));
  }

  void sendRequestFromFormData(BuildContext context) {
    final formDataBloc = context.read<FormDataBloc>();
    final headers = combineHeaders(context);

    final combinedFormData = <String, String>{};
    for (var row in formDataBloc.state.rows) {
      if (row.isSelected && row.key.isNotEmpty && row.value.isNotEmpty) {
        combinedFormData[row.key] = row.value;
      }
    }
    _sendRequestFormData(formData: combinedFormData, headers: headers);
  }

  void _sendRequestFormData({required Map<String, String> formData, required Map<String, String> headers}) {
    final url = _urlController.text.trim();
    if (url.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Please enter a valid URL.')));
      return;
    }

    // Show loading indicator
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Sending request...'),
        duration: Duration(seconds: 1),
      ),
    );

    // Convert formData to a Map<String, dynamic> for the body
    final Map<String, dynamic> body = {};
    formData.forEach((key, value) {
      body[key] = value;
    });

    // Use the PostmanRequestBloc to send the request
    context.read<PostmanRequestBloc>().add(SendDirectRequest(
      method: _selectedMethod,
      url: url,
      headers: headers,
      body: body,
      tabUuid: widget.uuid,
    ));
  }

  void sendRequestFromNone(BuildContext context) {
    final headers = combineHeaders(context);
    final url = _urlController.text.trim();
    if (url.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Please enter a valid URL.')));
      return;
    }

    // Show loading indicator
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Sending request...'),
        duration: Duration(seconds: 1),
      ),
    );

    // Use the PostmanRequestBloc to send the request
    context.read<PostmanRequestBloc>().add(SendDirectRequest(
      method: _selectedMethod,
      url: url,
      headers: headers,
      body: null,
      tabUuid: widget.uuid,
    ));
  }

}
