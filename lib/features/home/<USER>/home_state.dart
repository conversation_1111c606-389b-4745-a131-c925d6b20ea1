import 'package:equatable/equatable.dart';
import '../data/api_collection.dart';



enum LeftPanelType {
  collection,
  history,
}

class HomeState extends Equatable {
  final List<ApiCollection> apiData;
  final String searchQuery;
  final String searchQuerySaveRequest;
  final Map<String, bool> expandedSections;
  final List<TabModel> openTabs;
  final TabModel? activeTab;
  final bool forCollection;
  final LeftPanelType leftPanelType;
  final List<RequestHistoryModel> requestHistory;
  HomeState({
    required this.apiData,
    this.searchQuery = '',
    this.searchQuerySaveRequest = '',
    Map<String, bool>? expandedSections,
    required this.openTabs,
    this.activeTab,
    this.forCollection = true,
    this.leftPanelType = LeftPanelType.collection,
    this.requestHistory = const [],
  }) : expandedSections = expandedSections ?? {};

  HomeState copyWith({
    List<ApiCollection>? apiData,
    String? searchQuery,
    String? searchQuerySaveRequest,
    Map<String, bool>? expandedSections,
    List<TabModel>? openTabs,
    TabModel? activeTab,
    bool? forCollection,
    LeftPanelType? leftPanelType,
    List<RequestHistoryModel>? requestHistory,
  }) {
    return HomeState(
      apiData: apiData ?? this.apiData,
      searchQuery: searchQuery ?? this.searchQuery,
      searchQuerySaveRequest: searchQuerySaveRequest ?? this.searchQuerySaveRequest,
      expandedSections: expandedSections ?? this.expandedSections,
      openTabs: openTabs ?? this.openTabs,
      activeTab: activeTab ?? this.activeTab,
      forCollection: forCollection ?? this.forCollection,
      leftPanelType: leftPanelType ?? this.leftPanelType,
      requestHistory: requestHistory ?? this.requestHistory,
    );
  }

  @override
  List<Object?> get props => [apiData,
    searchQuery,searchQuerySaveRequest, expandedSections,openTabs,activeTab,forCollection,leftPanelType,requestHistory
];
}
