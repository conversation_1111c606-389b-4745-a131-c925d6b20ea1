import 'dart:collection';
import 'package:flutter/foundation.dart';

/// A cache for storing tab-specific data to prevent reloading when switching tabs
/// Uses LRU (Least Recently Used) caching strategy to limit memory usage
class TabCache {
  // Singleton instance
  static final TabCache _instance = TabCache._internal();
  factory TabCache() => _instance;

  // Maximum number of tabs to cache responses for
  static const int _maxCacheSize = 20;

  // LRU cache for response data, keyed by tab UUID
  final LinkedHashMap<String, dynamic> _responseCache = LinkedHashMap<String, dynamic>();

  // LRU cache for tab state (to preserve scroll positions, etc.)
  final LinkedHashMap<String, Map<String, dynamic>> _tabStateCache = LinkedHashMap<String, Map<String, dynamic>>();

  // Cache for formatted JSON strings to avoid repeated formatting
  final Map<String, String> _formattedJsonCache = {};

  TabCache._internal();

  /// Store response data for a specific tab
  void cacheResponse(String tabUuid, dynamic responseData) {
    // Remove oldest entry if cache is full
    if (_responseCache.length >= _maxCacheSize && !_responseCache.containsKey(tabUuid)) {
      _responseCache.remove(_responseCache.keys.first);
    }

    // Add or update the cache (puts it at the end of the LinkedHashMap)
    _responseCache.remove(tabUuid);
    _responseCache[tabUuid] = responseData;
  }

  /// Get cached response data for a specific tab
  dynamic getCachedResponse(String tabUuid) {
    final data = _responseCache[tabUuid];
    if (data != null) {
      // Move to end of LRU cache (most recently used)
      _responseCache.remove(tabUuid);
      _responseCache[tabUuid] = data;
    }
    return data;
  }

  /// Check if response data is cached for a specific tab
  bool hasResponseCache(String tabUuid) {
    return _responseCache.containsKey(tabUuid);
  }

  /// Cache a formatted JSON string to avoid repeated formatting
  void cacheFormattedJson(String tabUuid, String formattedJson) {
    _formattedJsonCache[tabUuid] = formattedJson;
  }

  /// Get cached formatted JSON string
  String? getFormattedJson(String tabUuid) {
    return _formattedJsonCache[tabUuid];
  }

  /// Store tab state
  void cacheTabState(String tabUuid, String key, dynamic value) {
    // Remove oldest entry if cache is full
    if (_tabStateCache.length >= _maxCacheSize && !_tabStateCache.containsKey(tabUuid)) {
      _tabStateCache.remove(_tabStateCache.keys.first);
    }

    // Add or update the cache
    _tabStateCache.putIfAbsent(tabUuid, () => {});
    _tabStateCache[tabUuid]![key] = value;

    // Move to end of LRU cache (most recently used)
    final state = _tabStateCache[tabUuid];
    _tabStateCache.remove(tabUuid);
    _tabStateCache[tabUuid] = state!;
  }

  /// Get cached tab state
  dynamic getTabState(String tabUuid, String key) {
    if (_tabStateCache.containsKey(tabUuid)) {
      final value = _tabStateCache[tabUuid]![key];

      // Move to end of LRU cache (most recently used)
      final state = _tabStateCache[tabUuid];
      _tabStateCache.remove(tabUuid);
      _tabStateCache[tabUuid] = state!;

      return value;
    }
    return null;
  }

  /// Clear cache for a specific tab
  void clearCache(String tabUuid) {
    _responseCache.remove(tabUuid);
    _tabStateCache.remove(tabUuid);
    _formattedJsonCache.remove(tabUuid);
  }

  /// Clear all caches
  void clearAllCaches() {
    _responseCache.clear();
    _tabStateCache.clear();
    _formattedJsonCache.clear();
  }

  /// Get memory usage statistics
  Map<String, int> getMemoryStats() {
    return {
      'responseCache': _responseCache.length,
      'tabStateCache': _tabStateCache.length,
      'formattedJsonCache': _formattedJsonCache.length,
    };
  }
}
