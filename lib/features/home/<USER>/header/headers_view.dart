import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/api_collection.dart';
import 'dart:async';
import '../../../../helpers/color_config.dart';
import '../../../../helpers/style_config.dart';
import '../../../common_widgets/CommonText.dart';
import '../../../common_widgets/custom_checkbox.dart';
import '../../bloc/home_bloc.dart';
import '../../bloc/home_event.dart';
import '../../bloc/home_state.dart';
import 'bloc/headers_bloc.dart';
import 'bloc/headers_event.dart';
import 'bloc/headers_state.dart';

class HeadersView extends StatefulWidget {
  final String uuid;
  final TabModel? tabModel;

  const HeadersView({
    required this.uuid,
    this.tabModel,
    super.key,
  });

  @override
  State<HeadersView> createState() => _HeadersViewState();
}

class _HeadersViewState extends State<HeadersView> {
  int? hoveredColumnIndex;
  bool isDragging = false;
  List<double> columnWidths = [1.0, 1.0, 2.0];
  bool _isInitialized = false;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!_isInitialized) {
        _isInitialized = true;
        _loadHeadersFromTabModel();
      }
    });
  }

  void _loadHeadersFromTabModel() {
    if (!mounted) return;

    final headersBloc = context.read<HeadersBloc>();
    final homeBloc = context.read<HomeBloc>();
    TabModel? tabModel = widget.tabModel;

    if (tabModel == null || tabModel.uuid != widget.uuid) {
      try {
        tabModel = homeBloc.state.openTabs.firstWhere(
          (tab) => tab.uuid == widget.uuid,
        );
      } catch (e) {
        if (homeBloc.state.activeTab?.uuid == widget.uuid) {
          tabModel = homeBloc.state.activeTab;
        }
      }
    }

    if (tabModel != null && tabModel.uuid == widget.uuid) {
      if (tabModel.headers != null && tabModel.headers!.isNotEmpty) {
        debugPrint('HeadersView: Loading headers from TabModel for ${widget.uuid}: ${tabModel.headers}');
        headersBloc.add(LoadHeadersEvent(tabModel.headers!, tabUuid: widget.uuid));
      } else {
        debugPrint('HeadersView: TabModel headers empty, initializing default headers for ${widget.uuid}');
        headersBloc.add(InitializeHeadersForTabEvent(tabUuid: widget.uuid));
      }
    } else {
      debugPrint('HeadersView: No matching tab found, initializing default headers for ${widget.uuid}');
      headersBloc.add(InitializeHeadersForTabEvent(tabUuid: widget.uuid));
    }
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }
  void _debouncedUpdateTabHeaders(BuildContext context) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      final homeBloc = context.read<HomeBloc>();
      final activeTab = homeBloc.state.activeTab;
      final headersBloc = context.read<HeadersBloc>();

      if (activeTab != null && activeTab.uuid == widget.uuid) {
        final updatedHeaders = headersBloc.getHeadersMapForTab(widget.uuid);
        homeBloc.add(UpdateHeadersEvent(uuid: activeTab.uuid, headers: updatedHeaders));
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      buildWhen: (previous, current) {
        if (previous.activeTab?.uuid != current.activeTab?.uuid) {
          return previous.activeTab?.uuid == widget.uuid || current.activeTab?.uuid == widget.uuid;
        }

        if (current.activeTab?.uuid == widget.uuid) {
          final previousHeaders = previous.activeTab?.headers;
          final currentHeaders = current.activeTab?.headers;
          if (previousHeaders != currentHeaders) {
            debugPrint('HeadersView: Headers changed for tab ${widget.uuid}, will reload from TabModel');
            return true;
          }
        }
        final currentTab = current.openTabs.firstWhere(
          (tab) => tab.uuid == widget.uuid,
          orElse: () => current.activeTab!,
        );
        final previousTab = previous.openTabs.firstWhere(
          (tab) => tab.uuid == widget.uuid,
          orElse: () => previous.activeTab!,
        );

        if (currentTab.uuid == widget.uuid && previousTab.uuid == widget.uuid) {
          if (currentTab.headers != previousTab.headers) {
            debugPrint('HeadersView: Tab headers updated for ${widget.uuid}, will reload from TabModel');
            return true;
          }
        }

        return false;
      },
      builder: (context, homeState) {
        final currentTab = homeState.openTabs.firstWhere(
          (tab) => tab.uuid == widget.uuid,
          orElse: () => homeState.activeTab!,
        );

        if (currentTab.uuid == widget.uuid && currentTab.headers != null && currentTab.headers!.isNotEmpty) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              _loadHeadersFromTabModel();
            }
          });
        }

        return BlocBuilder<HeadersBloc, HeadersState>(
          buildWhen: (previous, current) {
            if (current.tabUuid != widget.uuid) return false;
            if (previous.rows.length != current.rows.length) return true;
            if (previous.rows.length == current.rows.length && current.rows.isNotEmpty) {
              for (int i = 0; i < current.rows.length; i++) {
                if (previous.rows.length > i) {
                  final prevRow = previous.rows[i];
                  final currRow = current.rows[i];
                  if (prevRow.key != currRow.key || prevRow.value != currRow.value) {
                    debugPrint('HeadersView: Row content changed, rebuilding');
                    return true;
                  }
                }
              }
            }
            return false;
          },
          builder: (context, state) {
            if (state.tabUuid != widget.uuid && !_isInitialized) {
              _isInitialized = true;
              final headersBloc = context.read<HeadersBloc>();
              Future.microtask(() {
                if (mounted) {
                  headersBloc.add(InitializeHeadersForTabEvent(tabUuid: widget.uuid));
                }
              });
              return const Center(child: CircularProgressIndicator());
            }
            final rows = context.read<HeadersBloc>().getHeadersForTab(widget.uuid);

            return Padding(
              padding: const EdgeInsets.only(right: 20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.only(bottom: 10),
                    child: CommonText(
                      text: "Headers",
                      textStyle: mMediumTextStyle16(
                          textSize: 14, textColor: AppThemeColor.white),
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      scrollDirection: Axis.vertical,
                      child: Table(
                        border: TableBorder.all(
                          color: AppThemeColor.commonBorderColor
                        ),
                        columnWidths: {
                          0: const FixedColumnWidth(40),
                          1: FlexColumnWidth(columnWidths[0]),
                          2: FlexColumnWidth(columnWidths[1]),
                          3: FlexColumnWidth(columnWidths[2]),
                        },
                        defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                        children: [
                          _buildHeaderRow(),
                          ...List.generate(rows.length, (index) {
                            final row = rows[index];
                            return TableRow(
                              children: [
                                _buildCheckboxCell(context, index, row.isSelected),
                                _buildTextFieldCell(context, index, row.keyController, 'key', isStatic: row.isStatic),
                                _buildTextFieldCell(context, index, row.valueController, 'value'),
                                _buildTextFieldCell(context, index, row.descriptionController, 'description'),
                              ],
                            );
                          }),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  TableRow _buildHeaderRow() {
    return TableRow(
      children: [
        Container(),
        _buildResizableHeaderCell('Key', 0),
        _buildResizableHeaderCell('Value', 1),
        _buildResizableHeaderCell('Description', 2),
      ],
    );
  }

  Widget _buildResizableHeaderCell(String text, int columnIndex) {
    return MouseRegion(
      onEnter: (_) {
        setState(() {
          hoveredColumnIndex = columnIndex;
        });
      },
      onExit: (_) {
        setState(() {
          hoveredColumnIndex = null;
        });
      },
      child: Stack(
        children: [
          Container(
            height: 35,
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.symmetric(horizontal: 12.0),
            color: AppThemeColor.commonBackground,
            child: CommonText(
              text: text,
              textAlign: TextAlign.left,
              textStyle: mMediumTextStyle16(
                textSize: 14, textColor: AppThemeColor.tableHeaderTextColor
              ),
            ),
          ),
          if (hoveredColumnIndex == columnIndex || isDragging)
            Positioned(
              right: -10,
              top: 0,
              bottom: 0,
              child: GestureDetector(
                onHorizontalDragStart: (_) {
                  setState(() {
                    isDragging = true;
                  });
                },
                onHorizontalDragUpdate: (details) {
                  setState(() {
                    columnWidths[columnIndex] += details.primaryDelta! * 0.01;
                    columnWidths[columnIndex] =
                        columnWidths[columnIndex].clamp(0.5, 5.0);
                  });
                },
                onHorizontalDragEnd: (_) {
                  setState(() {
                    isDragging = false;
                  });
                },
                child: MouseRegion(
                  cursor: SystemMouseCursors.resizeColumn,
                  child: Container(
                    width: 20,
                    alignment: Alignment.center,
                    child: Icon(
                      Icons.drag_handle,
                      size: 16,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTextFieldCell(
    BuildContext context,
    int rowIndex,
    TextEditingController controller,
    String fieldName, {
    bool isStatic = false,
  }) {
    return Container(
      height: 35,
      padding: const EdgeInsets.symmetric(horizontal: 12.0),
      alignment: Alignment.centerLeft,
      child: TextField(
        controller: controller,
        textAlign: TextAlign.left,
        style: const TextStyle(
            color: AppThemeColor.tableHeaderTextColor, fontSize: 12),
        decoration: InputDecoration(
          contentPadding: EdgeInsets.zero,
          border: const OutlineInputBorder(
            borderSide: BorderSide.none,
          ),
          hintText: fieldName,
          hintStyle: const TextStyle(
            color: AppThemeColor.tableTextColor, fontSize: 12
          ),
        ),
        onChanged: (value) {
          final headersBloc = context.read<HeadersBloc>();
          headersBloc.add(
            UpdateRowEvent(
              rowIndex: rowIndex,
              key: fieldName == 'key' ? value : controller.text,
              value: fieldName == 'value' ? value : controller.text,
              description: fieldName == 'description' ? value : controller.text,
              tabUuid: widget.uuid,
            ),
          );
          _debouncedUpdateTabHeaders(context);
        },
      ),
    );
  }

  Widget _buildCheckboxCell(BuildContext context, int rowIndex, bool isSelected) {
    return Center(
      child: CustomCheckbox(
        value: isSelected,
        label: "",
        checkboxSize: 20,
        checkIconSize: 14,
        onChanged: (value) {
          context.read<HeadersBloc>().add(
            ToggleRowSelectionEvent(
              rowIndex: rowIndex,
              isSelected: value,
              tabUuid: widget.uuid,
            ),
          );
          _debouncedUpdateTabHeaders(context);
        },
      ),
    );
  }
}
