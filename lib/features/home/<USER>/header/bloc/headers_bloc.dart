import 'package:bloc/bloc.dart';
import 'package:flutter/cupertino.dart';
import 'dart:collection';

import 'headers_event.dart';
import 'headers_model.dart';
import 'headers_state.dart';

class HeadersBloc extends Bloc<HeadersEvent, HeadersState> {
  final Map<String, List<HeaderRow>> _tabHeadersMap = {};

  // Default headers for new tabs
  List<HeaderRow> _getDefaultHeaders() {
    return [
      HeaderRow(
        keyController: TextEditingController(text: "Content-Type"),
        valueController: TextEditingController(text: "application/json"),
        descriptionController: TextEditingController(),
        key: "Content-Type",
        value: "application/json",
        isStatic: false,
      ),
      HeaderRow(
        keyController: TextEditingController(text: "Authorization"),
        valueController: TextEditingController(text: "Bearer <token>"),
        descriptionController: TextEditingController(),
        key: "Authorization",
        value: "Bearer <token>",
        isStatic: false,
      ),
      HeaderRow(
        keyController: TextEditingController(text: ""),
        valueController: TextEditingController(text: ""),
        descriptionController: TextEditingController(),
        key: "",
        value: "",
        isStatic: false,
      ),
    ];
  }

  HeadersBloc(): super(HeadersState(rows: [])) {

    on<InitializeHeadersForTabEvent>((event, emit) {
      final tabUuid = event.tabUuid;
      if (tabUuid != null) {
        if (!_tabHeadersMap.containsKey(tabUuid)) {
          _tabHeadersMap[tabUuid] = _getDefaultHeaders();
        }

        emit(HeadersState(
          rows: _tabHeadersMap[tabUuid] ?? _getDefaultHeaders(),
          tabUuid: tabUuid,
        ));
      }
    });

    on<AddRowEvent>((event, emit) {
      print('AddRowEvent....');
      final tabUuid = event.tabUuid ?? state.tabUuid;
      if (tabUuid == null) return;

      final newRow = HeaderRow(
        keyController: TextEditingController(),
        valueController: TextEditingController(),
        descriptionController: TextEditingController(),
      );

      final currentRows = _tabHeadersMap[tabUuid] ?? state.rows;
      final newRows = List<HeaderRow>.from(currentRows)..add(newRow);

      // Update the map and emit new state
      _tabHeadersMap[tabUuid] = newRows;
      emit(state.copyWith(rows: newRows, tabUuid: tabUuid));
    });

    on<UpdateRowEvent>((event, emit) {
      print('UpdateRowEvent.......');
      final tabUuid = event.tabUuid ?? state.tabUuid;
      if (tabUuid == null) return;

      final currentRows = _tabHeadersMap[tabUuid] ?? state.rows;
      if (event.rowIndex >= currentRows.length) return;

      final updatedRows = List<HeaderRow>.from(currentRows);
      final row = updatedRows[event.rowIndex];

      updatedRows[event.rowIndex] = row.copyWith(
        key: row.keyController.text,
        value: row.valueController.text,
        description: row.descriptionController.text,
      );

      // Update the map and emit new state
      _tabHeadersMap[tabUuid] = updatedRows;
      emit(state.copyWith(rows: updatedRows, tabUuid: tabUuid));

      if (event.rowIndex == updatedRows.length - 1) {
        add(AddRowEvent(tabUuid: tabUuid));
      }
    });

    on<ToggleRowSelectionEvent>((event, emit) {
      final tabUuid = event.tabUuid ?? state.tabUuid;
      if (tabUuid == null) return;

      final currentRows = _tabHeadersMap[tabUuid] ?? state.rows;
      if (event.rowIndex >= currentRows.length) return;

      final updatedRows = List<HeaderRow>.from(currentRows);
      updatedRows[event.rowIndex] =
          updatedRows[event.rowIndex].copyWith(isSelected: event.isSelected);

      // Update the map and emit new state
      _tabHeadersMap[tabUuid] = updatedRows;
      emit(state.copyWith(rows: updatedRows, tabUuid: tabUuid));
    });

    on<LoadHeadersEvent>((event, emit) {
      final tabUuid = event.tabUuid ?? state.tabUuid;
      if (tabUuid == null) return;

      // If headers are empty, use default headers
      if (event.headers.isEmpty) {
        final defaultHeaders = _getDefaultHeaders();
        _tabHeadersMap[tabUuid] = defaultHeaders;
        emit(state.copyWith(rows: defaultHeaders, tabUuid: tabUuid));
        return;
      }

      final newRows = event.headers.entries.map((entry) {
        return HeaderRow(
          keyController: TextEditingController(text: entry.key),
          valueController: TextEditingController(text: entry.value),
          descriptionController: TextEditingController(),
          key: entry.key,
          value: entry.value,
          isSelected: true,
        );
      }).toList();

      // Add an empty row at the end for new entries
      newRows.add(HeaderRow(
        keyController: TextEditingController(),
        valueController: TextEditingController(),
        descriptionController: TextEditingController(),
      ));

      // Update the map and emit new state
      _tabHeadersMap[tabUuid] = newRows;
      emit(state.copyWith(rows: newRows, tabUuid: tabUuid));
    });

    on<ResetHeadersEvent>((event, emit) {
      final tabUuid = event.tabUuid ?? state.tabUuid;
      if (tabUuid == null) return;

      // Remove the tab's headers from the map
      _tabHeadersMap.remove(tabUuid);

      // If this is the current tab, reset the state
      if (tabUuid == state.tabUuid) {
        emit(HeadersState(rows: [], tabUuid: tabUuid));
      }
    });
  }

  List<HeaderRow> getHeadersForTab(String tabUuid) {
    if (!_tabHeadersMap.containsKey(tabUuid)) {
      _tabHeadersMap[tabUuid] = _getDefaultHeaders();
    }
    return _tabHeadersMap[tabUuid] ?? [];
  }

  // Helper method to get headers as Map<String, String> for a specific tab
  Map<String, String> getHeadersMapForTab(String tabUuid) {
    final headers = _tabHeadersMap[tabUuid] ?? [];
    return headers
        .where((row) => row.isSelected && row.key.isNotEmpty && row.value.isNotEmpty)
        .fold<Map<String, String>>({}, (map, row) {
      map[row.key] = row.value;
      return map;
    });
  }
}