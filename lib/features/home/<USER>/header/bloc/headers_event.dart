
import 'package:equatable/equatable.dart';

import 'headers_model.dart';

abstract class HeadersEvent extends Equatable {
  final String? tabUuid; // Add tabUuid to all events

  const HeadersEvent({this.tabUuid});

  @override
  List<Object?> get props => [tabUuid];
}

class AddRowEvent extends HeadersEvent {
  const AddRowEvent({String? tabUuid}) : super(tabUuid: tabUuid);
}

class UpdateRowEvent extends HeadersEvent {
  final int rowIndex;
  final String key;
  final String value;
  final String description;

  const UpdateRowEvent({
    required this.rowIndex,
    required this.key,
    required this.value,
    required this.description,
    String? tabUuid,
  }) : super(tabUuid: tabUuid);

  @override
  List<Object?> get props => [rowIndex, key, value, description, tabUuid];
}

class ToggleRowSelectionEvent extends HeadersEvent {
  final int rowIndex;
  final bool isSelected;

  const ToggleRowSelectionEvent({
    required this.rowIndex,
    required this.isSelected,
    String? tabUuid,
  }) : super(tabUuid: tabUuid);

  @override
  List<Object?> get props => [rowIndex, isSelected, tabUuid];
}

class LoadHeadersEvent extends HeadersEvent {
  final Map<String, String> headers;

  const LoadHeadersEvent(
    this.headers, {
    String? tabUuid,
  }) : super(tabUuid: tabUuid);

  @override
  List<Object?> get props => [headers, tabUuid];
}

class ResetHeadersEvent extends HeadersEvent {
  const ResetHeadersEvent({String? tabUuid}) : super(tabUuid: tabUuid);
}

class InitializeHeadersForTabEvent extends HeadersEvent {
  const InitializeHeadersForTabEvent({required String tabUuid}) : super(tabUuid: tabUuid);
}


