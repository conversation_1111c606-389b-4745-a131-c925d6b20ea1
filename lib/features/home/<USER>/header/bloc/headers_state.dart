import 'headers_model.dart';
import 'package:equatable/equatable.dart';

// State: Holds the current list of rows and the associated tab UUID
class HeadersState extends Equatable {
  final List<HeaderRow> rows;
  final String? tabUuid; // Add tabUuid to track which tab this state belongs to

  const HeadersState({
    required this.rows,
    this.tabUuid,
  });

  HeadersState copyWith({
    List<HeaderRow>? rows,
    String? tabUuid,
  }) {
    return HeadersState(
      rows: rows ?? this.rows,
      tabUuid: tabUuid ?? this.tabUuid,
    );
  }

  @override
  List<Object?> get props => [rows, tabUuid];
}
