import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';

import '../data/api_collection.dart';


abstract class HomeEvent extends Equatable {
  @override
  // TODO: implement props
  List<Object?> get props => [];
}

// Left Panel Events
class LoadApiDataEvent extends HomeEvent {}

class AddCollectionEvent extends HomeEvent {
  final String collectionName;
  AddCollectionEvent(this.collectionName);
}

class AddFolderEvent extends HomeEvent {
  final String collectionName;
  final String folderName;
  final String? parentFolderName;

  AddFolderEvent(this.collectionName, this.folderName, {this.parentFolderName});
}

class AddRequestEvent extends HomeEvent {
  final String collectionName;
  final String? folderName;
  final String requestName;
  final String method;
   final String? url;
   final Map<String, dynamic>? jsonData;

  AddRequestEvent({
    required this.collectionName,
    required this.folderName,
    required this.requestName,
    required this.method,
     this.url,
     this.jsonData,
  });
  @override
  List<Object?> get props => [collectionName, folderName, requestName, method, url, jsonData];
}

class RenameRequestEvent extends HomeEvent {
  final String requestId; // UUID of the request
  final String newName;
  final String collectionName;
  final String? folderName;

  RenameRequestEvent({
    required this.requestId,
    required this.newName,
    required this.collectionName,
    this.folderName,
  });
}

class RenameFolderEvent extends HomeEvent {
  final String collectionName;
  final String oldFolderName;
  final String newFolderName;

  RenameFolderEvent({
    required this.collectionName,
    required this.oldFolderName,
    required this.newFolderName,
  });

  @override
  List<Object?> get props => [collectionName, oldFolderName, newFolderName];
}

class RenameCollectionEvent extends HomeEvent {
  final String oldCollectionName;
  final String newCollectionName;

  RenameCollectionEvent({
    required this.oldCollectionName,
    required this.newCollectionName,
  });

  @override
  List<Object?> get props => [oldCollectionName, newCollectionName];
}

class DuplicateRequestEvent extends HomeEvent {
  final String requestId; // UUID of the request
  final String collectionName;
  final String? folderName;

  DuplicateRequestEvent({
    required this.requestId,
    required this.collectionName,
    this.folderName,
  });
}

class DeleteItemEvent extends HomeEvent {
  final String name;
  final String? collectionName;
  final String? folderName;
  final int? index;
  DeleteItemEvent({
    required this.name,
    this.collectionName,
    this.folderName,
    this.index,
  });
}

class DeleteRequestEvent extends HomeEvent {
  final String requestId; // UUID of the request
  final String collectionName;
  final String? folderName;

  DeleteRequestEvent({
    required this.requestId,
    required this.collectionName,
    this.folderName,
  });
}

class DeleteFolderEvent extends HomeEvent {
  final String folderName;
  final String collectionName;

  DeleteFolderEvent({
    required this.folderName,
    required this.collectionName,
  });
}

class SearchQueryChangedEvent extends HomeEvent {
  final String query;
  SearchQueryChangedEvent(this.query);
}

class SearchQueryChangedEventSaveRequest extends HomeEvent {
  final String querySaveReuest;
  SearchQueryChangedEventSaveRequest(this.querySaveReuest);
}

class ToggleExpansionEvent extends HomeEvent {
  final String sectionId;
  final int? index;
  ToggleExpansionEvent(this.sectionId, {this.index});
}

class OpenTabEvent extends HomeEvent {
  final TabModel tabModel; // Use TabModel instead of raw data

  OpenTabEvent({
    required this.tabModel,
  });

  @override
  List<Object?> get props => [tabModel];
}


class UpdateDialogTabEvent extends HomeEvent {
  final List<TabModel> updatedTabs;

  UpdateDialogTabEvent(this.updatedTabs);

  @override
  List<Object?> get props => [updatedTabs];
}

class RemoveTabEvent extends HomeEvent {
  final String requestId; // UUID of the request to remove the tab for

  RemoveTabEvent({required this.requestId});
}

class CloseTabEvent extends HomeEvent {
  final String uuid;
  CloseTabEvent(this.uuid);
}

class SwitchTabEvent extends HomeEvent {
  final String? uuid;
  final String? requestId;
  final bool preferRequestId; // Flag to indicate whether to prefer requestId over uuid

  SwitchTabEvent({
    this.uuid,
    this.requestId,
    this.preferRequestId = false,
  }) : assert(uuid != null || requestId != null, 'Either uuid or requestId must be provided');

  @override
  List<Object?> get props => [uuid, requestId, preferRequestId];
}

class ChangeMethodEvent extends HomeEvent {
  final String uuid;
  final String method;
  ChangeMethodEvent({required this.uuid, required this.method});
}

class ChangeUrlEvent extends HomeEvent {
  final String uuid;
  final String url;
  ChangeUrlEvent({required this.uuid, required this.url});
}

class UpdateJsonDataEventTab extends HomeEvent {
  final Map<String, dynamic> jsonData;
  UpdateJsonDataEventTab(this.jsonData);
}

class UpdateResponseJsonDataEventTab extends HomeEvent {
  final Map<String, dynamic> responseJsonData;
  UpdateResponseJsonDataEventTab(this.responseJsonData);
}

class ToggleForCollectionEvent extends HomeEvent {
  final bool forCollection;

  ToggleForCollectionEvent(this.forCollection);

  @override
  List<Object?> get props => [forCollection];
}

// Event to navigate into a folder
class NavigateIntoFolderEvent extends HomeEvent {
  final String folderName;

  NavigateIntoFolderEvent(this.folderName);
}

// Event to navigate out of a folder
class NavigateOutOfFolderEvent extends HomeEvent {}

class SwitchToCollectionPanelEvent extends HomeEvent {}

class SwitchToHistoryPanelEvent extends HomeEvent {}

class SaveRequestToHistoryEvent extends HomeEvent {
  final String requestName;
  final String method;
  final String url;
  final Map<String, dynamic>? jsonData;

  SaveRequestToHistoryEvent({
    required this.requestName,
    required this.method,
    required this.url,
    this.jsonData,
  });

  @override
  List<Object?> get props => [requestName, method, url, jsonData];
}

class LoadRequestHistoryEvent extends HomeEvent {}

class DeleteRequestFromHistoryEvent extends HomeEvent {
  final String id;

  DeleteRequestFromHistoryEvent(this.id);

  @override
  List<Object?> get props => [id];
}

class ClearRequestHistoryEvent extends HomeEvent {}

class UpdateRequestEvent extends HomeEvent {
  final String requestId;
  final String collectionName;
  final String? folderName;
  final String requestName;
  final String method;
  final String url;
  final Map<String, dynamic>? jsonData;

  UpdateRequestEvent({
    required this.requestId,
    required this.collectionName,
    this.folderName,
    required this.requestName,
    required this.method,
    required this.url,
    this.jsonData,
  });

  @override
  List<Object?> get props => [requestId, collectionName, folderName, requestName, method, url, jsonData];
}

class UpdateHeadersEvent extends HomeEvent {
  final String uuid;
  final Map<String, String> headers;

  UpdateHeadersEvent({required this.uuid, required this.headers});

  @override
  List<Object> get props => [uuid, headers];
}

class UpdateTabEvent extends HomeEvent {
  final String uuid;
  final bool? isModified;
  final String? tabName;
  final String? method;
  final String? url;
  final Map<String, dynamic>? jsonData;
  final Map<String, String>? headers;
  final String? originalTabName;
  final String? originalMethod;
  final String? originalUrl;
  final Map<String, dynamic>? originalJsonData;
  final Map<String, String>? originalHeaders;

  UpdateTabEvent(
    this.uuid, {
    this.isModified,
    this.tabName,
    this.method,
    this.url,
    this.jsonData,
    this.headers,
    this.originalTabName,
    this.originalMethod,
    this.originalUrl,
    this.originalJsonData,
    this.originalHeaders,
  });

  @override
  List<Object?> get props => [uuid, isModified, tabName, method, url, jsonData, headers, originalTabName, originalMethod, originalUrl, originalJsonData, originalHeaders];
}





