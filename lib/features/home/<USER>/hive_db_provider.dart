import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive/hive.dart';
import 'package:postman_flutter/features/home/<USER>/api_collection.dart';

import '../../common_widgets/common_snackbar.dart';
import '../tabs_views/header/bloc/headers_bloc.dart';
import '../tabs_views/header/bloc/headers_model.dart';


const String API_COLLECTION_BOX = 'apiCollectionBox';
const String REQUEST_HISTORY_BOX = 'requestHistoryBox';

class HiveDBService {
  Box<dynamic>? _apiCollectionBox;
  Box<dynamic>? _requestHistoryBox;

  static final HiveDBService _singleton = HiveDBService._internal();

  factory HiveDBService() => _singleton;

  HiveDBService._internal();

  Future<void> open() async {
    try {
      _apiCollectionBox = await Hive.openBox(API_COLLECTION_BOX);
    } catch (e) {
      if (kDebugMode) {
        print('Error opening Hive box: $e');
      }
    }
  }

  Future<void> openRequestHistoryBox() async {
    try {
      _requestHistoryBox = await Hive.openBox(REQUEST_HISTORY_BOX);
    } catch (e) {
      if (kDebugMode) {
        print('Error opening request history box: $e');
      }
    }
  }

  Future<void> close() async {
    try {
      if (_apiCollectionBox != null && _apiCollectionBox!.isOpen) {
        await _apiCollectionBox!.close();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error closing Hive box: $e');
      }
    }
  }


  Future<void> saveApiCollection(List<ApiCollection> collections) async {
    try {
      if (_apiCollectionBox == null || !_apiCollectionBox!.isOpen) {
        await open();
      }
      final jsonList = collections.map((e) => e.toJson()).toList();
      await _apiCollectionBox!.put('apiCollections', jsonEncode(jsonList));
    } catch (e) {
      if (kDebugMode) {
        print('Error saving collections: $e');
      }
    }
  }

  Future<List<ApiCollection>> getApiCollections() async {
    try {
      if (_apiCollectionBox == null || !_apiCollectionBox!.isOpen) {
        await open();
      }
      final jsonString = _apiCollectionBox!.get('apiCollections');

      print('apiCollection: ${jsonString}');

      if (jsonString != null) {
        final jsonList = jsonDecode(jsonString) as List<dynamic>;
        return jsonList
            .map((e) => ApiCollection.fromJson(e as Map<String, dynamic>))
            .toList();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading collections: $e');
      }
    }
    return [];
  }

  Future<void> addCollection(ApiCollection collection) async {
  try {
  final collections = await getApiCollections();

  if (_isNameExists(collections, collection.collectionName!)) {
  throw Exception('A collection with this name already exists');
  }

  collections.add(collection);
  await saveApiCollection(collections);
  } catch (e) {
    SnackbarService().showSnackbar('A collection with this name already exists', Colors.red);
  }
  }

  bool _isNameExists(List<ApiCollection> collections, String name) {
    return collections.any((c) => c.collectionName == name);
  }

  Future<void> addFolder(
      String collectionName,
      ApiFolder folder, {
        String? parentFolderName,
      }) async {
    try {
      if (folder.folderName == null || folder.folderName!.trim().isEmpty) {
        SnackbarService().showSnackbar('Folder name cannot be empty', Colors.red);
        return;
      }

      final collections = await getApiCollections();
      final collection = collections.firstWhere(
            (c) => c.collectionName == collectionName,
        orElse: () {
          SnackbarService().showSnackbar('Collection not found', Colors.red);
          throw Exception('Collection not found');
        },
      );

      if (parentFolderName != null) {
        final parentFolder = _findFolderRecursive(collection.children, parentFolderName);
        if (parentFolder != null) {
          if (_isFolderNameExists(parentFolder.children ?? [], folder.folderName!)) {
            SnackbarService().showSnackbar('A folder with this name already exists', Colors.red);
            return;
          }
          parentFolder.children ??= [];
          parentFolder.children!.add(folder);
        } else {
          SnackbarService().showSnackbar('Parent folder not found', Colors.red);
          throw Exception('Parent folder not found');
        }
      } else {
        if (_isFolderNameExists(collection.children ?? [], folder.folderName!)) {
          SnackbarService().showSnackbar('A folder with this name already exists', Colors.red);
          return;
        }
        collection.children ??= [];
        collection.children!.add(folder);
      }

      await saveApiCollection(collections);
    } catch (e) {
      if (kDebugMode) {
        print('Error adding folder: $e');
      }
    }
  }

  bool _isFolderNameExists(List<ApiFolder> folders, String name) {
    return folders.any((folder) =>
    folder.folderName?.trim().toLowerCase() == name.trim().toLowerCase()
    );
  }


  //
  // bool _isFolderNameExists(List<ApiFolder> folders, String name) {
  //   return folders.any((folder) => folder.folderName == name);
  // }

  // Map<String, String> combineHeaders(BuildContext context) {
  //   final headersBloc = context.read<HeadersBloc>();
  //
  //   final combinedHeaders = <String, String>{};
  //
  //   for (var row in headersBloc.state.rows) {
  //     if (row.isSelected && row.key.isNotEmpty && row.value.isNotEmpty) {
  //       combinedHeaders[row.key] = row.value;
  //     }
  //   }
  //
  //   return combinedHeaders;
  // }

  Future<void> addRequest({
    required String collectionName,
    required String folderName,
    required ApiFolder request,
    String? requestName,
    String? method,
    String? url,
    Map<String, dynamic>? jsonData,
    Map<String, String>? headers,
  }) async {
    final collections = await getApiCollections();
    final collection = collections.firstWhere(
          (c) => c.collectionName == collectionName,
      orElse: () => throw Exception('Collection not found'),
    );

    if (folderName.isNotEmpty) {
      // Check for duplicate name in the folder
      final folder = _findFolderRecursive(collection.children, folderName);
      if (folder != null) {
        // if (_isNameExists(folder.children, request.requestData!)) {
        //   throw Exception('A request with this name already exists');
        // }
        folder.children ??= [];
        folder.children!.add(request);
      } else {
        throw Exception('Folder not found');
      }
    } else {
      // Check for duplicate name in the collection
      // if (_isNameExists(collection.children, request.requestData!)) {
      //   throw Exception('A request with this name already exists');
      // }
      collection.children ??= [];
      collection.children!.add(request);
    }

    print('hhh:  ${request.headers.toString()}');

    // Save additional data if provided (from TabView)
    if (requestName != null && method != null && url != null) {
      request.requestData = requestName;
      request.method = method;
      request.url = url;
      request.jsonData = jsonData;
      request.headers = headers;
    }

    await saveApiCollection(collections);
  }

  Future<void> deleteItem({
    required String name,
    String? collectionName,
    String? folderName,
    int? index,
  }) async {
    final collections = await getApiCollections();

    if (collectionName == null) {
      // Delete collection by index
      if (index != null && index < collections.length) {
        collections.removeAt(index);
      }
    } else {
      final collection = collections.firstWhere(
            (c) => c.collectionName == collectionName,
        orElse: () => throw Exception('Collection not found'),
      );

      if (folderName == null) {
        // Delete folder or request directly under the collection by index
        if (index != null && index < (collection.children?.length ?? 0)) {
          collection.children?.removeAt(index);
        }
      } else {
        // Delete folder or request inside a folder by index
        final folder = _findFolderRecursive(collection.children, folderName);
        if (folder != null && index != null && index < (folder.children?.length ?? 0)) {
          folder.children?.removeAt(index);
        }
      }
    }

    await saveApiCollection(collections);
  }

  // Helper method to find a folder recursively
  ApiFolder? _findFolderRecursive(List<ApiFolder>? folders, String folderName) {
    if (folders == null) return null;

    for (final folder in folders) {
      if (folder.folderName == folderName) {
        return folder;
      }
      final found = _findFolderRecursive(folder.children, folderName);
      if (found != null) {
        return found;
      }
    }
    return null;
  }

  Future<void> renameItem({
    required String oldName,
    required String newName,
    String? collectionName,
    String? folderName,
  }) async {
    final collections = await getApiCollections();

    if (collectionName == null) {
      // Rename collection
      final collection = collections.firstWhere(
            (c) => c.collectionName == oldName,
        orElse: () => throw Exception('Collection not found'),
      );
      collection.collectionName = newName;
    } else if (folderName == null) {
      // Rename folder
      final collection = collections.firstWhere(
            (c) => c.collectionName == collectionName,
        orElse: () => throw Exception('Collection not found'),
      );
      final folder = collection.children?.firstWhere(
            (f) => f.folderName == oldName,
        orElse: () => throw Exception('Folder not found'),
      );
      folder?.folderName = newName;
    } else {
      // Rename request
      final collection = collections.firstWhere(
            (c) => c.collectionName == collectionName,
        orElse: () => throw Exception('Collection not found'),
      );
      final folder = collection.children?.firstWhere(
            (f) => f.folderName == folderName,
        orElse: () => throw Exception('Folder not found'),
      );
      final request = folder?.children?.firstWhere(
            (r) => r.requestData == oldName,
        orElse: () => throw Exception('Request not found'),
      );
      request?.requestData = newName;
    }
    await saveApiCollection(collections);
  }


  Future<void> clearDatabase() async {
    try {
      if (_apiCollectionBox != null && _apiCollectionBox!.isOpen) {
        await _apiCollectionBox!.clear();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing database: $e');
      }
    }
  }


  // Save a request to history
  Future<void> saveRequestToHistory(RequestHistoryModel request) async {
    try {
      if (_requestHistoryBox == null || !_requestHistoryBox!.isOpen) {
        await openRequestHistoryBox();
      }
      await _requestHistoryBox!.put(request.id, jsonEncode(request.toJson()));
    } catch (e) {
      if (kDebugMode) {
        print('Error saving request to history: $e');
      }
    }
  }


  Future<List<RequestHistoryModel>> getRequestHistory() async {
    try {
      if (_requestHistoryBox == null || !_requestHistoryBox!.isOpen) {
        await openRequestHistoryBox();
      }

      // Retrieve all values from the box
      final historyData = _requestHistoryBox!.values.toList();

      // Convert each item to a Map<String, dynamic>
      final historyList = historyData.map((jsonn) {
        Map<String, dynamic> data=jsonDecode(jsonn.toString());
        // Ensure the JSON data is a Map<String, dynamic>
        final Map<String, dynamic> jsonMap = data;
        return RequestHistoryModel.fromJson(jsonMap);
            }).toList();

      return historyList;
    } catch (e) {
      if (kDebugMode) {
        print('Error loading request history: $e');
      }
      return [];
    }
  }



  Future<void> deleteRequestFromHistory(String id) async {
    try {
      if (_requestHistoryBox == null || !_requestHistoryBox!.isOpen) {
        await openRequestHistoryBox();
      }
      await _requestHistoryBox!.delete(id);
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting request from history: $e');
      }
    }
  }


  Future<void> clearRequestHistory() async {
    try {
      if (_requestHistoryBox == null || !_requestHistoryBox!.isOpen) {
        await openRequestHistoryBox();
      }
      await _requestHistoryBox!.clear();
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing request history: $e');
      }
    }
  }

}


// bool _isFNameExists(List<ApiFolder>? folders, String name) {
//   if (folders == null) return false;
//   return folders.any((f) => f.folderName == name || f.requestData == name);
// }



