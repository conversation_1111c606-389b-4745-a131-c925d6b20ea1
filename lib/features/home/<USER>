import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_code_editor/flutter_code_editor.dart';
import 'package:multi_split_view/multi_split_view.dart';
import 'package:postman_flutter/api_constant.dart';
import 'package:postman_flutter/features/home/<USER>/home_state.dart';
import 'package:postman_flutter/features/home/<USER>/collection/collection_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/folder/folder_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/postmanrequest/postman_request_bloc.dart';
import 'package:postman_flutter/repo/collectionRepo.dart';
import 'package:postman_flutter/model/collection_detail_model.dart';
import 'package:postman_flutter/features/home/<USER>/postmanrequest/postman_request_event.dart';
import 'package:postman_flutter/features/home/<USER>/postmanrequest/postman_request_state.dart';
import 'package:postman_flutter/features/home/<USER>/authorization/authorization.dart';
import 'package:postman_flutter/features/home/<USER>/authorization/bloc/auth_subviews_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/authorization/bloc/auth_subviews_event.dart';
import 'package:postman_flutter/features/home/<USER>/body/body.dart' as BodyWidget;
import 'package:postman_flutter/features/home/<USER>/body/form_data/bloc/form_data_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/body/form_data/bloc/form_data_event.dart';
import 'package:postman_flutter/features/home/<USER>/body/json_data/bloc/json_data_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/body/json_data/bloc/json_data_event.dart';
import 'package:postman_flutter/features/home/<USER>/header/bloc/headers_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/header/bloc/headers_event.dart';
import 'package:postman_flutter/features/home/<USER>/header/headers_view.dart';
import 'package:postman_flutter/features/home/<USER>/query_params/bloc/query_params_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/query_params/bloc/query_params_event.dart';
import 'package:postman_flutter/features/home/<USER>/query_params/query_params_view.dart';
import 'package:postman_flutter/features/home/<USER>/response_view.dart';
import 'package:postman_flutter/features/home/<USER>/scripts.dart';
import 'package:postman_flutter/features/home/<USER>/send_request/request_sender_view.dart';
import 'package:postman_flutter/features/home/<USER>/settings.dart';
import 'package:postman_flutter/features/common_widgets/dynamic_radio_tabs/bloc/tab_bloc.dart' as radio_tab;
import 'package:postman_flutter/features/common_widgets/dynamic_radio_tabs/bloc/tab_event.dart' as radio_tab_event;
import 'package:postman_flutter/helpers/color_config.dart';
import 'package:postman_flutter/helpers/common.dart';
import 'package:uuid/uuid.dart';
import '../../globals.dart';
import '../../helpers/style_config.dart';
import '../common_widgets/CommonAlertDialog.dart';
import '../common_widgets/CommonButton.dart';
import '../common_widgets/CommonIconTextButton.dart';
import '../common_widgets/CommonSVGIcon.dart';
import '../common_widgets/CommonText.dart';
import '../common_widgets/CommonTextButton.dart';
import '../common_widgets/CommonTextFormField.dart';
import '../common_widgets/common_dropdown.dart';
import '../common_widgets/common_snackbar.dart';
import '../common_widgets/dynamic_tabs_widget.dart';
import 'bloc/home_bloc.dart';
import 'bloc/home_event.dart';
import 'data/api_collection.dart';
import 'data/hive_db_provider.dart';
import '../../model/collection_model.dart';
import '../../model/collection_detail_model.dart';

enum AreaType { topPanel, bottomPanel }

class TabManager extends ValueNotifier<Object?> {
  final List<TabModel> openTabs = []; // Store TabModel objects
  TabModel? activeTab;

  TabManager() : super(null);

  void openTab(TabModel tab) {
    final existingTabIndex = openTabs.indexWhere((t) => t.uuid == tab.uuid);

    if (existingTabIndex != -1) {
      activeTab = openTabs[existingTabIndex];
    } else {
      openTabs.add(tab);
      activeTab = tab;
    }

    notifyListeners();
  }

  void closeTab(String uuid) {
    openTabs.removeWhere((tab) => tab.uuid == uuid);

    if (activeTab?.uuid == uuid) {
      activeTab = openTabs.isNotEmpty ? openTabs.last : null;
    }

    notifyListeners();
  }

  void switchTab(String uuid) {
    final tab = openTabs.firstWhere((tab) => tab.uuid == uuid, orElse: () => throw Exception('Tab not found'));
    activeTab = tab;
    notifyListeners();
  }
}


class TabView extends StatefulWidget {
  @override
  State<TabView> createState() => _TabViewState();
}

class _TabViewState extends State<TabView> with AutomaticKeepAliveClientMixin {
  final MultiSplitViewController _verticalController = MultiSplitViewController();
  final ScrollController _scrollControllerCollection = ScrollController();
  final ScrollController _scrollControllerFolder = ScrollController();
  final ScrollController _scrollControllerTabbar = ScrollController();
  final ScrollController scrollControllerTabbar = ScrollController();
  late HomeBloc homeBloc;

  // Keep this widget alive when it's not visible
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _verticalController.areas = [
      Area(flex: 1, min: 0.5),
      Area(flex: 1, min: 0.4),
    ];
    homeBloc = BlocProvider.of<HomeBloc>(context,listen: false);
    WidgetsBinding.instance.addPostFrameCallback((_){
      homeBloc.add(LoadApiDataEvent());
    });
  }

  void _scrollToNewTab() {
    final scrollController = ScrollControllerProvider.of(context)?.scrollControllerTabbar;

    Future.delayed(Duration(milliseconds: 300), () {
      if (scrollController != null && scrollController.hasClients) {
        scrollController.animateTo(
          scrollController.position.maxScrollExtent,
          duration: Duration(milliseconds: 500),
          curve: Curves.easeOut,
        );
      }
    });
  }

  bool _isTabModified(TabModel tab) {
    if (tab.originalTabName == null && tab.originalMethod == null &&
        tab.originalUrl == null && tab.originalJsonData == null && tab.originalHeaders == null) {
      return false;
    }

    final tabNameMatches = tab.tabName == (tab.originalTabName ?? tab.tabName);
    final methodMatches = tab.method == (tab.originalMethod ?? tab.method);
    final urlMatches = tab.url == (tab.originalUrl ?? tab.url);
    final jsonDataMatches = _mapsEqual(tab.jsonData, tab.originalJsonData);
    final headersMatches = _mapsEqual(tab.headers, tab.originalHeaders);

    final isModified = !(
      tab.tabName == (tab.originalTabName ?? tab.tabName) &&
      tab.method == (tab.originalMethod ?? tab.method) &&
      tab.url == (tab.originalUrl ?? tab.url) &&
      _mapsEqual(tab.jsonData, tab.originalJsonData) &&
      _mapsEqual(tab.headers, tab.originalHeaders)
    );
    // debugPrint('tab.jsonData:' + tab.jsonData.toString() + ' tab.originalJsonData:' + tab.originalJsonData.toString() + ')');
    // debugPrint('Tab ${tab.uuid} modification check:');
    // debugPrint('  - TabName: ${tab.tabName} vs ${tab.originalTabName} = $tabNameMatches');
    // debugPrint('  - Method: ${tab.method} vs ${tab.originalMethod} = $methodMatches');
    // debugPrint('  - URL: ${tab.url} vs ${tab.originalUrl} = $urlMatches');
    // debugPrint('  - JsonData matches: $jsonDataMatches');
    // debugPrint('  - Headers matches: $headersMatches');
    // debugPrint('  - Final isModified: $isModified');

    return isModified;
  }


  // bool _mapsEqual<K, V>(Map<K, V>? map1, Map<K, V>? map2) {
  //   if (map1 == null && map2 == null) return true;
  //   if (map1 == null || map2 == null) return false;
  //   if (map1.length != map2.length) return false;

  //   for (final key in map1.keys) {
  //     if (!map2.containsKey(key) || map1[key] != map2[key]) {
  //       return false;
  //     }
  //   }
  //   return true;
  // }

  bool _deepEquals(dynamic a, dynamic b) {
    if (a is Map && b is Map) {
      if (a.length != b.length) return false;
      for (final key in a.keys) {
        if (!b.containsKey(key) || !_deepEquals(a[key], b[key])) {
          return false;
        }
      }
      return true;
    } else if (a is List && b is List) {
      if (a.length != b.length) return false;
      for (int i = 0; i < a.length; i++) {
        if (!_deepEquals(a[i], b[i])) return false;
      }
      return true;
    } else {
      return a == b;
    }
  }

  bool _mapsEqual<K, V>(Map<K, V>? map1, Map<K, V>? map2) {
    if (map1 == null && map2 == null) return true;
    if (map1 == null || map2 == null) return false;
    return _deepEquals(map1, map2);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return BlocBuilder<HomeBloc, HomeState>(
      buildWhen: (previous, current) {
        if (previous.openTabs.length != current.openTabs.length) {
          return true;
        }

        if (previous.activeTab?.uuid != current.activeTab?.uuid) {
          debugPrint('TabManager: Rebuilding due to active tab change: ${previous.activeTab?.uuid} -> ${current.activeTab?.uuid}');
          return true;
        }

        if (previous.activeTab != null && current.activeTab != null &&
            previous.activeTab!.uuid == current.activeTab!.uuid) {
          final shouldRebuild = previous.activeTab!.url != current.activeTab!.url ||
                 previous.activeTab!.method != current.activeTab!.method ||
                 previous.activeTab!.tabName != current.activeTab!.tabName ||
                 !_mapsEqual(previous.activeTab!.jsonData, current.activeTab!.jsonData) ||
                 !_mapsEqual(previous.activeTab!.headers, current.activeTab!.headers) ||
                 !_mapsEqual(previous.activeTab!.originalJsonData, current.activeTab!.originalJsonData) ||
                 !_mapsEqual(previous.activeTab!.originalHeaders, current.activeTab!.originalHeaders) ||
                 previous.activeTab!.originalTabName != current.activeTab!.originalTabName ||
                 previous.activeTab!.originalMethod != current.activeTab!.originalMethod ||
                 previous.activeTab!.originalUrl != current.activeTab!.originalUrl;

          if (shouldRebuild) {
            debugPrint('TabManager: Rebuilding due to active tab data change for tab ${current.activeTab!.uuid}');
            debugPrint('  - URL: ${previous.activeTab!.url} -> ${current.activeTab!.url}');
            debugPrint('  - Method: ${previous.activeTab!.method} -> ${current.activeTab!.method}');
            debugPrint('  - TabName: ${previous.activeTab!.tabName} -> ${current.activeTab!.tabName}');
            debugPrint('  - JsonData changed: ${!_mapsEqual(previous.activeTab!.jsonData, current.activeTab!.jsonData)}');
            debugPrint('  - Headers changed: ${!_mapsEqual(previous.activeTab!.headers, current.activeTab!.headers)}');
          }

          return shouldRebuild;
        }

        for (int i = 0; i < current.openTabs.length && i < previous.openTabs.length; i++) {
          final prevTab = previous.openTabs[i];
          final currTab = current.openTabs[i];

          if (prevTab.uuid == currTab.uuid) {
            final dataChanged = prevTab.url != currTab.url ||
                               prevTab.method != currTab.method ||
                               prevTab.tabName != currTab.tabName ||
                               !_mapsEqual(prevTab.jsonData, currTab.jsonData) ||
                               !_mapsEqual(prevTab.headers, currTab.headers) ||
                               !_mapsEqual(prevTab.originalJsonData, currTab.originalJsonData) ||
                               !_mapsEqual(prevTab.originalHeaders, currTab.originalHeaders) ||
                               prevTab.originalTabName != currTab.originalTabName ||
                               prevTab.originalMethod != currTab.originalMethod ||
                               prevTab.originalUrl != currTab.originalUrl;

            if (dataChanged) {
              debugPrint('TabManager: Rebuilding due to data change for tab ${currTab.uuid}');
              return true;
            }
          }
        }

        return false;
      },
      builder: (context, state) {
        final activeTab = state.activeTab;
        //print('active tab uuid :::: ${activeTab?.headers}');
        if (state.openTabs.isEmpty) {
          return Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(5.0),
                    child: InkWell(
                      onTap: () {
                        context.read<HomeBloc>().add(
                          OpenTabEvent(
                            tabModel: TabModel(
                              uuid: Uuid().v4(),
                              tabName: "New Request",
                              method: "GET",
                              url: "",
                              jsonData: {},
                              collectionName: '',
                              folderName: '',
                              requestId: null,
                            ),
                          ),
                        );
                      },
                      child: Container(
                        padding: EdgeInsets.all(5),
                        child: Icon(
                          Icons.add,
                          color: Colors.white70,
                          size: 16,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              Expanded(
                child: Center(
                  child: CommonText(
                    text: "No tabs open",
                    textStyle: mMediumTextStyle16(
                      textSize: 12,
                      textColor: AppThemeColor.white,
                    ),
                  ),
                ),
              ),
            ],
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Tab Bar
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Container(
                    margin: EdgeInsets.zero,
                    padding: EdgeInsets.zero,
                    //color: AppThemeColor.commonBackground,
                    color: Colors.amber, //rohan_changed
                    child: Row(
                      children: [
                        Flexible(
                          child: SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            controller: _scrollControllerTabbar,
                            child: Row(
                              children: state.openTabs.map((tab) {
                                bool isActive = tab.uuid == activeTab?.uuid;
                                return GestureDetector(
                                  onTap: () {
                                    print('Tab Tapped: ${tab.uuid} :: ${tab.tabName} -- ${activeTab?.uuid} :: ${activeTab?.tabName}');
                                    if (tab.uuid != activeTab?.uuid) {
                                      Future.microtask(() {
                                        if (mounted) {
                                          // Use request ID for switching if available, otherwise fall back to UUID
                                          if (tab.requestId != null && tab.requestId!.isNotEmpty) {
                                            context.read<HomeBloc>().add(SwitchTabEvent(
                                              uuid: tab.uuid,
                                              requestId: tab.requestId,
                                              preferRequestId: true,
                                            ));
                                          } else {
                                            context.read<HomeBloc>().add(SwitchTabEvent(
                                              uuid: tab.uuid,
                                            ));
                                          }
                                        }
                                      });
                                    }
                                  },
                                  child: Container(
                                    margin: EdgeInsets.only(left: 1, right: 1, top: 0, bottom: 0),
                                    padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                                    decoration: BoxDecoration(
                                      //color: isActive ? AppThemeColor.tabbarBgColor : AppThemeColor.tabbarBgColor,
                                      color: Colors.pink,//rohan_changed
                                      border: isActive
                                          ? const Border(
                                        bottom: BorderSide(
                                          color: AppThemeColor.tabbarUnderLineColor,
                                          width: 2,
                                        ),
                                      )
                                          : null,
                                    ),
                                    child: Row(
                                      children: [
                                        CommonText(
                                          text: tab.method,
                                          textStyle: mRegularTextStyle16(
                                            textSize: 12,
                                            textColor: AppThemeColor.white,
                                          ),
                                        ),
                                        SizedBox(width: 10),
                                        Row(
                                          children: [
                                            CommonText(
                                              text: tab.tabName,
                                              maxLength: 15,
                                              textStyle: mMediumTextStyle16(
                                                textSize: 12,
                                                textColor: AppThemeColor.tabbarTextColor,
                                              ),
                                            ),

                                            if (_isTabModified(tab)) ...[
                                              const SizedBox(width: 3),
                                              Container(
                                                width: 6,
                                                height: 6,
                                                decoration: const BoxDecoration(
                                                  color: Colors.orange,
                                                  shape: BoxShape.circle,
                                                ),
                                              ),
                                            ],
                                          ],
                                        ),
                                        const SizedBox(width: 5),

                                        MouseRegion(
                                          cursor: SystemMouseCursors.click,
                                          child: GestureDetector(
                                            onTap: () {
                                              final activeTab = context.read<HomeBloc>().state.activeTab;
                                              if (activeTab != null && _isTabModified(activeTab)) {
                                                // Show the close tab alert dialog if the tab has unsaved changes
                                                CommonCloseTabAlertDialog.show(
                                                  context: context,
                                                  title: 'Unsaved Changes',
                                                  buttonCreateText: 'Save',
                                                  description: 'You have unsaved changes. Do you want to save them before closing?',
                                                  inputFields: [],
                                                  onCancel: () {
                                                    Navigator.of(context).pop();
                                                  },
                                                  onSaveChanges: () {
                                                    Navigator.of(context).pop();
                                                    debugPrint('Save button clicked ====> ${activeTab.requestId}');
                                                    // Check if the request is already saved
                                                    if(!isCheckEmpty(activeTab.requestId)) {
                                                      saveRequestToDB(activeTab, context);
                                                    }else{
                                                      showSaveRequestDialog(context, activeTab);
                                                    }
                                                  },
                                                  onDontSave: () {
                                                    Navigator.of(context).pop();
                                                    context.read<HomeBloc>().add(CloseTabEvent(tab.uuid)); // Close the tab
                                                  },
                                                );
                                              } else {
                                                context.read<HomeBloc>().add(CloseTabEvent(tab.uuid));
                                              }
                                            },
                                            child: Icon(
                                              Icons.close,
                                              color: Colors.white70,
                                              size: 16,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              }).toList(),
                            ),
                          ),
                        ),
                        InkWell(
                          onTap: () {
                            context.read<HomeBloc>().add(
                              OpenTabEvent(
                                tabModel: TabModel(
                                  uuid: Uuid().v4(),
                                  tabName: "New Request ${state.openTabs.length}",
                                  method: "GET",
                                  url: "",
                                  jsonData: {},
                                  responseJsonData: {},
                                 // headers: {},
                                  collectionName: '',
                                  folderName: '',
                                  requestId: null,
                                ),
                              ),
                            );
                            Future.delayed(Duration(milliseconds: 300), () {
                              if (_scrollControllerTabbar.hasClients) {
                                _scrollControllerTabbar.animateTo(
                                  _scrollControllerTabbar.position.maxScrollExtent,
                                  duration: Duration(milliseconds: 500),
                                  curve: Curves.easeOut,
                                );
                              }
                            });

                          },
                          child: Container(
                            padding: EdgeInsets.all(5),
                            child: Icon(
                              Icons.add,
                              color: Colors.white70,
                              size: 16,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),

            // Tab Content
            Expanded(
              child: Container(
                color: AppThemeColor.commonBackground,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 50,
                      padding: EdgeInsets.only(left: 20, right: 20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CommonText(
                            text: activeTab?.tabName ?? '',
                            textStyle: mMediumTextStyle16(
                              textSize: 14,
                              textColor: AppThemeColor.white,
                            ),
                          ),
                          BlocListener<PostmanRequestBloc, PostmanRequestState>(
                            listener: (context, state) {
                              if (state is RequestSaved && state.tabUuid == activeTab?.uuid) {

                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(state.response['message'] ?? 'Request saved successfully'),
                                    backgroundColor: Colors.green,
                                  ),
                                );

                                final updatedTab = activeTab!.withOriginalState();
                                context.read<HomeBloc>().add(
                                  UpdateTabEvent(
                                    activeTab.uuid,
                                    tabName: updatedTab.tabName,
                                    method: updatedTab.method,
                                    url: updatedTab.url,
                                    headers: updatedTab.headers,
                                    jsonData: updatedTab.jsonData,
                                    originalTabName: updatedTab.originalTabName,
                                    originalMethod: updatedTab.originalMethod,
                                    originalUrl: updatedTab.originalUrl,
                                    originalJsonData: updatedTab.originalJsonData,
                                    originalHeaders: updatedTab.originalHeaders,
                                    isModified: false,
                                  ),
                                );
                              } else if (state is RequestSaveError && state.tabUuid == activeTab?.uuid) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(state.message),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              } else if (state is RequestDetailsFetched) {
                                print('RequestDetailsFetched called in TabManager');
                                final requestDetails = state.requestDetails;
                                final homeBloc = context.read<HomeBloc>();
                                final headersBloc = context.read<HeadersBloc>();
                                final currentActiveTab = homeBloc.state.activeTab;
                                if (currentActiveTab != null) {
                                  Map<String, String> headers = {};
                                  if (requestDetails.headers.isNotEmpty) {
                                    requestDetails.headers.forEach((key, value) {
                                      if (value is String) {
                                        headers[key] = value;
                                      } else {
                                        headers[key] = value.toString();
                                      }
                                    });
                                  }

                                  // Load headers into HeadersBloc for the current tab
                                  debugPrint('Loading headers into HeadersBloc for tab ${currentActiveTab.uuid}: $headers');
                                  headersBloc.add(LoadHeadersEvent(headers, tabUuid: currentActiveTab.uuid));

                                  Map<String, dynamic> jsonData = {};
                                  if (requestDetails.body.type == 'json') {
                                    try {
                                      jsonData = requestDetails.body.getContentAsJson();
                                    } catch (e) {
                                      debugPrint('Error parsing JSON body: $e');
                                    }
                                  }
                                  // Update tab with fetched data and set original state
                                  final updatedTab = currentActiveTab.copyWith(
                                    tabName: requestDetails.name,
                                    method: requestDetails.method,
                                    url: requestDetails.url,
                                    headers: headers,
                                    jsonData: jsonData,
                                    isModified: false,
                                  ).withOriginalState(); // Set current state as original

                                  homeBloc.add(
                                    UpdateTabEvent(
                                      currentActiveTab.uuid,
                                      tabName: updatedTab.tabName,
                                      method: updatedTab.method,
                                      url: updatedTab.url,
                                      headers: updatedTab.headers,
                                      jsonData: updatedTab.jsonData,
                                      originalTabName: updatedTab.originalTabName,
                                      originalMethod: updatedTab.originalMethod,
                                      originalUrl: updatedTab.originalUrl,
                                      originalJsonData: updatedTab.originalJsonData,
                                      originalHeaders: updatedTab.originalHeaders,
                                      isModified: false,
                                    ),
                                  );
                                }
                              } else if (state is RequestDetailsFetchError) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(state.message),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            },
                            child: CommonTextButton(
                              text: 'Save',
                              enabled: activeTab != null ? _isTabModified(activeTab) : false,
                              textStyle: const TextStyle(color: AppThemeColor.textButtonBlue),
                              callback: () {
                                print('activeTab?.requestId while saving.... ${activeTab?.requestId}');
                                if(!isCheckEmpty(activeTab?.requestId)) {
                                  saveRequestToDB(activeTab, context);
                                }else{
                                  showSaveRequestDialog(context, activeTab,);
                                }

                                // if (activeTab != null) {
                                //   final headersBloc = context.read<HeadersBloc>();
                                //   final headers = headersBloc.state.rows.where((row) => row.isSelected && row.key.isNotEmpty).fold<Map<String, String>>({}, (map, row) {
                                //     map[row.key] = row.value;
                                //     return map;
                                //   });

                                //   final bodyData = {
                                //     "type": "json",
                                //     "content": jsonEncode(activeTab.jsonData)
                                //   };

                                //   print('bodyData while saving: $bodyData');


                                //   context.read<PostmanRequestBloc>().add(
                                //     SaveRequest(
                                //       name: activeTab.tabName,
                                //       url: activeTab.url ?? "",
                                //       method: activeTab.method,
                                //       headers: headers,
                                //       body: bodyData,
                                //       params: {}, // Empty params for now
                                //       auth: {"type": "none"}, // Default auth type
                                //       collectionId: CommonConstants.collectionID.toString(),
                                //       tabUuid: activeTab.uuid,
                                //     ),
                                //   );
                                // }
                              },
                            ),
                          ),
                        ],
                      ),
                    ),

                    RequestSenderView(
                      uuid: activeTab?.uuid ?? '',
                    ),

                    Expanded(
                      child: MultiSplitViewTheme(
                        data: MultiSplitViewThemeData(
                          dividerThickness: 2,
                          dividerPainter: DividerPainters.background(color: AppThemeColor.dividerBackgroundColor),
                        ),
                        child: MultiSplitView(
                          axis: Axis.vertical,
                          controller: _verticalController,
                          builder: (context, area) {
                            final index = _verticalController.areas.indexOf(area);
                            if (index == 0) {
                              print('hello............${activeTab?.uuid}');
                              return Container(
                                padding: const EdgeInsets.only(left: 20, right: 0, bottom: 0),
                                child: DynamicTabsWidget(
                                  tabTitles: ['Params', 'Authorization', 'Headers', 'Body'],
                                  tabViews: [
                                    QueryParamsView(),
                                    Authorization(),
                                    HeadersView(
                                      key: ValueKey(activeTab?.uuid ?? ''),
                                      uuid: activeTab?.uuid ?? '',
                                      tabModel: activeTab,
                                    ),
                                    BodyWidget.Body(
                                      uuid: activeTab?.uuid ?? '',
                                      tabModel: activeTab,
                                    ),
                                  ],
                                ),
                              );
                            } else if (index == 1) {
                              return Container(
                                padding: EdgeInsets.only(left: 20, top: 10, bottom: 0),
                                child: ResponseView(
                                  key: ValueKey(activeTab?.uuid ?? ''),
                                  uuid: activeTab?.uuid ?? '',
                                ),
                              );
                            }
                            return const SizedBox.shrink();
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> showSaveRequestDialog(BuildContext context, TabModel? activeTab) async {
    if (activeTab == null) return;

    // Trigger collection loading from API
    context.read<CollectionBloc>().add(FetchCollections(ApiConstant.cmsWorkspaceId));

    final requestNameController = TextEditingController(text: activeTab.tabName);

    // Get current tab data from blocs
    final headersBloc = context.read<HeadersBloc>();
    final headers = headersBloc.state.rows
        .where((row) => row.isSelected && row.key.isNotEmpty)
        .fold<Map<String, String>>({}, (map, row) {
      map[row.key] = row.value;
      return map;
    });

    final queryParamsBloc = context.read<QueryParamsBloc>();
    final params = queryParamsBloc.state.rows
        .where((row) => row.isSelected && row.key.isNotEmpty)
        .fold<Map<String, String>>({}, (map, row) {
      map[row.key] = row.value;
      return map;
    });

    final bodyData = {
      "type": "json",
      "content": jsonEncode(activeTab.jsonData ?? {})
    };

    showDialog(
      context: context,
      builder: (dialogContext) => _SaveRequestDialog(
        activeTab: activeTab,
        requestNameController: requestNameController,
        headers: headers,
        params: params,
        bodyData: bodyData,
      ),
    );
  }




}

void saveRequestToDB(TabModel? activeTab, BuildContext context) {
  if (activeTab != null) {
    final headersBloc = context.read<HeadersBloc>();
    final headers = headersBloc.state.rows.where((row) => row.isSelected && row.key.isNotEmpty).fold<Map<String, String>>({}, (map, row) {
      map[row.key] = row.value;
      return map;
    });

    final bodyData = {
      "type": "json",
      "content": jsonEncode(activeTab.jsonData)
    };

    print('bodyData while saving: $bodyData');


    context.read<PostmanRequestBloc>().add(
      SaveRequest(
        name: activeTab.tabName,
        url: activeTab.url ?? "",
        method: activeTab.method,
        headers: headers,
        body: bodyData,
        params: {}, // Empty params for now
        auth: {"type": "none"}, // Default auth type
        collectionId: CommonConstants.collectionID.toString(),
        tabUuid: activeTab.uuid,
      ),
    );
  }
}

extension IterableExtension<T> on Iterable<T> {
  T? firstWhereOrNull(bool Function(T) test) {
    for (var element in this) {
      if (test(element)) {
        return element;
      }
    }
    return null;
  }
}

class _SaveRequestDialog extends StatefulWidget {
  final TabModel activeTab;
  final TextEditingController requestNameController;
  final Map<String, String> headers;
  final Map<String, String> params;
  final Map<String, String> bodyData;

  const _SaveRequestDialog({
    required this.activeTab,
    required this.requestNameController,
    required this.headers,
    required this.params,
    required this.bodyData,
  });

  @override
  State<_SaveRequestDialog> createState() => _SaveRequestDialogState();
}

class _SaveRequestDialogState extends State<_SaveRequestDialog> {
  final searchController = TextEditingController();
  String? selectedCollectionId;
  String? selectedCollectionName;
  String? selectedFolderId;
  String? selectedFolderName;
  bool showCollectionsList = true;
  CollectionBloc? _dialogCollectionBloc;
  int _refreshKey = 0;

  final Map<int, bool> expandedFolders = {};
  List<Folder> breadcrumbPath = [];
  Folder? currentFolder;

  List<Folder> _localFolderCache = [];
  bool _hasLocalUpdates = false;

  @override
  void initState() {
    super.initState();
    context.read<CollectionBloc>().add(FetchCollections(ApiConstant.cmsWorkspaceId));
  }


  void _addOptimisticFolder(String folderName) {
    if (mounted) {
      setState(() {
        // Create a temporary folder with a negative ID to distinguish it
        final tempFolder = Folder(
          id: -DateTime.now().millisecondsSinceEpoch, // Negative ID for temp folders
          name: folderName,
          collectionId: int.tryParse(selectedCollectionId ?? ''),
          parentFolderId: currentFolder?.id, // Set parent folder ID if we're in a subfolder
          folders: [],
        );

        _localFolderCache.add(tempFolder);
        _hasLocalUpdates = true;
        _refreshKey++;
        debugPrint('Added optimistic folder: $folderName with parentId: ${currentFolder?.id}');
      });
    }
  }

  @override
  void dispose() {
    _dialogCollectionBloc?.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: AppThemeColor.lightBackground,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(4.0),
      ),
      title: const Text(
        'Save New Request',
        style: TextStyle(color: AppThemeColor.white, fontSize: 18),
      ),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.4,
        height: MediaQuery.of(context).size.height * 0.6,
        child: BlocListener<PostmanRequestBloc, PostmanRequestState>(
          listener: (context, state) {
            if (state is PostmanRequestCreated) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message ?? 'Request created successfully!'),
                  backgroundColor: Colors.green,
                ),
              );

              if (mounted) {
                final currentActiveTab = context.read<HomeBloc>().state.activeTab;
                if (currentActiveTab != null) {
                  print('PostmanRequestCreated: Calling saveRequestToDB for tab ${currentActiveTab.uuid} ::: ${state.request.id}');
                  ApiConstant.requestID = state.request.id;
                  CommonConstants.collectionID = state.request.collectionId;

                  context.read<HomeBloc>().add(
                    UpdateTabEvent(
                      currentActiveTab.uuid,
                      tabName: widget.requestNameController.text,
                    ),
                  );

                  final updatedTab = currentActiveTab.copyWith(
                    requestId: state.request.id.toString(),
                    tabName: widget.requestNameController.text,
                  );

                  saveRequestToDB(updatedTab, context);
                } else {
                  print('PostmanRequestCreated: No active tab found to save');
                }
              }

              // context.read<CollectionBloc>().add(FetchCollections(ApiConstant.cmsWorkspaceId)); //rohan

              Navigator.of(context).pop();
            } else if (state is PostmanRequestCreationError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
          child: BlocListener<CollectionBloc, CollectionState>(
            listener: (context, state) {
              if (state is CollectionCreated) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.green,
                  ),
                );
                // Refresh collections
                context.read<CollectionBloc>().add(FetchCollections(ApiConstant.cmsWorkspaceId));
              } else if (state is CollectionCreationError) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: BlocListener<FolderBloc, FolderState>(
              listener: (context, state) {
                if (state is FolderCreated) {
                  print('Folder created successfully in dialog! Message: ${state.message}');
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.green,
                    ),
                  );

                  if (mounted && selectedCollectionId != null) {
                    setState(() {
                      _localFolderCache.clear();
                      _hasLocalUpdates = false;
                      _refreshKey++;
                      print('Cleared optimistic cache and incremented refresh key to: $_refreshKey');
                    });

                    final newFolder = state.newFolder.toFolder();
                    print('New folder details: id=${newFolder.id}, name=${newFolder.name}, parentId=${newFolder.parentFolderId}');

                    if (_dialogCollectionBloc != null) {
                      print('Adding folder to dialog collection bloc');
                      _dialogCollectionBloc!.add(AddFolderToCollectionDetail(
                        collectionId: int.parse(selectedCollectionId!),
                        newFolder: newFolder,
                      ));
                    }

                    context.read<CollectionBloc>().add(AddFolderToCollectionDetail(
                      collectionId: int.parse(selectedCollectionId!),
                      newFolder: newFolder,
                    ));

                    if (newFolder.parentFolderId != null && currentFolder?.id == newFolder.parentFolderId) {
                      setState(() {
                        _refreshKey++;
                      });
                    }
                  }

                  // Reduced delay for better user experience
                  Future.delayed(const Duration(milliseconds: 500), () {
                    if (mounted && selectedCollectionId != null && _dialogCollectionBloc != null) {
                      print('Backup refresh: Fetching fresh collection detail');
                      _dialogCollectionBloc!.add(FetchCollectionDetail(
                        collectionId: int.parse(selectedCollectionId!),
                        workspaceId: int.parse(ApiConstant.cmsWorkspaceId),
                      ));
                    }
                  });
                } else if (state is FolderCreationError) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              child: _buildDialogContent(),
            ),
          ),
        ),
      ),
    );
  }


  Widget _buildDialogContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Request Name Field
        CommonTextFormField(
          labelText: "Request Name",
          hintTextString: "Enter Request Name",
          isLabelText: true,
          isLabelTextBold: true,
          fontSize: 14,
          hintStyle: const TextStyle(color: AppThemeColor.hintTextColor),
          labelTextSize: Globals.labelTextSizeForDialog,
          textEditController: widget.requestNameController,
          cornerRadius: 4,
          errorMessage: "",
        ),
        const SizedBox(height: 20),

        // Navigation breadcrumb
        Row(
          children: [
            const CommonText(
              text: 'Save to ',
              textStyle: TextStyle(
                fontSize: 14,
                color: AppThemeColor.white,
              ),
            ),
            if (showCollectionsList) ...[
              const CommonText(
                text: 'Select a collection',
                textStyle: TextStyle(
                  fontSize: 14,
                  color: AppThemeColor.tabUnselectedTextColor,
                ),
              ),
            ] else ...[
              GestureDetector(
                onTap: () {
                  setState(() {
                    showCollectionsList = true;
                    selectedCollectionName = null;
                    selectedFolderName = null;
                    selectedCollectionId = null;
                    selectedFolderId = null;
                    breadcrumbPath.clear();
                    currentFolder = null;
                  });
                  _dialogCollectionBloc?.close();
                  _dialogCollectionBloc = null;

                  context.read<CollectionBloc>().add(FetchCollections(ApiConstant.cmsWorkspaceId));
                },
                child: const CommonText(
                  text: 'Collections',
                  textStyle: TextStyle(
                    fontSize: 14,
                    color: AppThemeColor.tabUnselectedTextColor,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
              if (selectedCollectionName != null) ...[
                GestureDetector(
                  onTap: () {
                    // Navigate back to collection root
                    setState(() {
                      breadcrumbPath.clear();
                      selectedFolderId = null;
                      selectedFolderName = null;
                      currentFolder = null;
                    });
                  },
                  child: CommonText(
                    text: ' / $selectedCollectionName',
                    textStyle: const TextStyle(
                      fontSize: 14,
                      color: AppThemeColor.white,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ),
              ],
              // Display breadcrumb path for nested folders
              ...breadcrumbPath.asMap().entries.map((entry) {
                final index = entry.key;
                final folder = entry.value;
                return GestureDetector(
                  onTap: () {
                    // Navigate to this folder level
                    setState(() {
                      breadcrumbPath = breadcrumbPath.sublist(0, index + 1);
                      selectedFolderId = folder.id?.toString();
                      selectedFolderName = folder.name;
                      currentFolder = folder;
                    });
                  },
                  child: CommonText(
                    text: ' / ${folder.name}',
                    textStyle: const TextStyle(
                      fontSize: 14,
                      color: AppThemeColor.white,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                );
              }),
            ],
          ],
        ),
        const SizedBox(height: 10),

        // Search Field
        CommonTextFormField(
          hintTextString: "Search",
          isLabelText: false,
          fontSize: 14,
          padding: const EdgeInsets.symmetric(vertical: 10),
          contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 17),
          hintStyle: const TextStyle(color: AppThemeColor.hintTextColor),
          textEditController: searchController,
          cornerRadius: Globals.defaultTextInputCornerRadius,
          isDense: true,
          errorMessage: "",
          onChange: (value) {
            setState(() {});
          },
        ),
        const SizedBox(height: 10),

        // Collections/Folders List
        Expanded(
          child: showCollectionsList
            ? BlocBuilder<CollectionBloc, CollectionState>(
                builder: (context, collectionState) {
                  print('Main CollectionBloc state: $collectionState');

                  if (collectionState is CollectionLoading) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (collectionState is CollectionError) {
                    return Center(child: Text('Error: ${collectionState.message}', style: const TextStyle(color: Colors.red)));
                  }


                  if (collectionState is CollectionLoaded) {
                    return _buildCollectionsList(collectionState);
                  } else if (collectionState is CollectionDetailLoaded) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      context.read<CollectionBloc>().add(FetchCollections(ApiConstant.cmsWorkspaceId));
                    });
                    return const Center(child: CircularProgressIndicator());
                  } else {
                    // If collections are not loaded, try to load them again
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      context.read<CollectionBloc>().add(FetchCollections(ApiConstant.cmsWorkspaceId));
                    });
                    return const Center(child: Text('Loading collections...', style: TextStyle(color: Colors.white70)));
                  }
                },
              )
            : (_dialogCollectionBloc != null
                ? BlocBuilder<CollectionBloc, CollectionState>(
                    key: ValueKey('dialog_folders_$_refreshKey'),
                    bloc: _dialogCollectionBloc,
                    builder: (context, collectionState) {
                      print('Dialog CollectionBloc state: $collectionState');

                      if (collectionState is CollectionLoading || collectionState is CollectionDetailLoading) {
                        return const Center(child: CircularProgressIndicator());
                      }

                      if (collectionState is CollectionError || collectionState is CollectionDetailError) {
                        final errorMessage = collectionState is CollectionError
                            ? collectionState.message
                            : (collectionState as CollectionDetailError).message;
                        return Center(child: Text('Error: $errorMessage', style: const TextStyle(color: Colors.red)));
                      }

                      if (collectionState is CollectionDetailLoaded) {
                        // Show folders for selected collection
                        print('Building folders list with ${collectionState.collectionDetail?.folders?.length ?? 0} folders');
                        return _buildFoldersList(collectionState);
                      }

                      if (selectedCollectionId != null && _dialogCollectionBloc != null) {
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          _dialogCollectionBloc!.add(FetchCollectionDetail(
                            collectionId: int.parse(selectedCollectionId!),
                            workspaceId: int.parse(ApiConstant.cmsWorkspaceId),
                          ));
                        });
                      }

                      return const Center(child: Text('Loading folders...', style: TextStyle(color: Colors.white70)));
                    },
                  )
                : const Center(child: Text('Loading...', style: TextStyle(color: Colors.white70)))),
        ),

        const SizedBox(height: 20),

        // Bottom Action Buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (showCollectionsList) ...[

              TextButton(
                onPressed: () {
                  _showCreateCollectionDialog(context);
                },
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
                child: const Text(
                  'New Collection',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
              ),
            ] else ...[
              Row(
                children: [
                  TextButton(
                    onPressed: () {
                      _showAddFolderDialog();
                    },
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    child: const Text(
                      'New Folder',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                  ),
                  /*if (selectedFolderId != null) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.blue, width: 1),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'Selected: $selectedFolderName',
                            style: const TextStyle(
                              color: Colors.blue,
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(width: 4),
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                selectedFolderId = null;
                                selectedFolderName = null;
                              });
                            },
                            child: const Icon(
                              Icons.close,
                              color: Colors.blue,
                              size: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],*/
                ],
              ),
            ],

            // Cancel and Save buttons on the right
            Row(
              children: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    backgroundColor: Colors.transparent,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                      side: const BorderSide(color: Colors.grey),
                    ),
                  ),
                  child: const Text(
                    'Cancel',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: selectedCollectionId != null || selectedFolderId != null
                      ? () {
                          if (selectedCollectionId != null) {
                            _saveRequest(selectedCollectionId!, selectedFolderId);
                          }
                        }
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    disabledBackgroundColor: Colors.grey,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  child: const Text(
                    'Save',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Folder? _findFolderById(List<Folder> folders, int? folderId) {
    if (folderId == null) return null;

    for (final folder in folders) {
      if (folder.id == folderId) {
        return folder;
      }
      if (folder.folders != null && folder.folders!.isNotEmpty) {
        final found = _findFolderById(folder.folders!, folderId);
        if (found != null) {
          return found;
        }
      }
    }
    return null;
  }

  Widget _buildCollectionsList(CollectionLoaded collectionState) {
    final collections = collectionState.collections;
    final searchQuery = searchController.text.toLowerCase();
    final filteredCollections = collections
        .where((collection) =>
        collection.collectionTitle!.toLowerCase().contains(searchQuery))
        .toList();

    return ListView.builder(
      itemCount: filteredCollections.length,
      itemBuilder: (context, index) {
        final collection = filteredCollections[index];
        return Material(
          type: MaterialType.transparency,
          child: ListTile(
            hoverColor: Colors.grey.withValues(alpha: 0.1),
            contentPadding: const EdgeInsets.only(left: 0),
            visualDensity: const VisualDensity(horizontal: 0, vertical: -4),
            title: Row(
              children: [
                const CommonSVGIcon(
                  imageName: 'collection',
                  imagePath: 'images',
                  color: AppThemeColor.white,
                  height: 20,
                  width: 20,
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: CommonText(
                    text: collection.collectionTitle ?? 'Unnamed Collection',
                    textStyle: const TextStyle(
                      fontSize: 14,
                      color: AppThemeColor.white,
                    ),
                  ),
                ),
              ],
            ),
            onTap: () {
              setState(() {
                selectedCollectionId = collection.collectionId.toString();
                selectedCollectionName = collection.collectionTitle;
                showCollectionsList = false;
                breadcrumbPath.clear(); 
                selectedFolderId = null;
                selectedFolderName = null;
                currentFolder = null;
              });
              if (_dialogCollectionBloc == null) {
                _dialogCollectionBloc = CollectionBloc(repository: CollectionRepository());
                _dialogCollectionBloc!.add(FetchCollections(ApiConstant.cmsWorkspaceId));
              }
              _dialogCollectionBloc!.add(FetchCollectionDetail(
                collectionId: collection.collectionId!,
                workspaceId: int.parse(ApiConstant.cmsWorkspaceId),
              ));
            },
          ),
        );
      },
    );
  }

  Widget _buildFoldersList(CollectionDetailLoaded collectionState) {
    final collectionDetail = collectionState.collectionDetail;

    List<Folder> folders;
    if (currentFolder != null) {
      final updatedCurrentFolder = _findFolderById(collectionDetail?.folders ?? [], currentFolder!.id);
      if (updatedCurrentFolder != null) {
        currentFolder = updatedCurrentFolder; 
        folders = updatedCurrentFolder.folders ?? [];
        debugPrint('Showing subfolders of "${updatedCurrentFolder.name}": ${folders.length} folders');
      } else {
        folders = currentFolder!.folders ?? [];
        debugPrint('Using cached subfolder data for "${currentFolder!.name}": ${folders.length} folders');
      }
    } else {
      folders = collectionDetail?.folders ?? [];
      debugPrint('Showing root folders of collection: ${folders.length} folders');
    }

    for (var folder in folders) {
      debugPrint('Processing folder: id=${folder.id}, name=${folder.name}, parentId=${folder.parentFolderId}');
    }

    if (_hasLocalUpdates && _localFolderCache.isNotEmpty) {
      final allFolders = List<Folder>.from(folders);
      for (final localFolder in _localFolderCache) {
        bool shouldAdd = false;
        if (currentFolder != null) {
          shouldAdd = localFolder.parentFolderId == currentFolder!.id;
        } else {
          shouldAdd = localFolder.parentFolderId == null;
        }

        if (shouldAdd && !allFolders.any((f) => f.name == localFolder.name)) {
          allFolders.add(localFolder);
        }
      }
      folders = allFolders;
      debugPrint('Using ${folders.length} folders (${_localFolderCache.length} from cache)');
    }


    final searchQuery = searchController.text.toLowerCase();
    final filteredFolders = folders
        .where((folder) =>
        folder.name?.toLowerCase().contains(searchQuery) ?? false)
        .toList();

    return Column(
      children: [
        // Save to Current Level Option
        Material(
          type: MaterialType.transparency,
          child: ListTile(
            hoverColor: Colors.grey.withValues(alpha: 0.1),
            contentPadding: const EdgeInsets.only(left: 0),
            visualDensity: const VisualDensity(horizontal: 0, vertical: -4),
            title: Row(
              children: [
                CommonSVGIcon(
                  imageName: currentFolder != null ? 'folder2' : 'collection',
                  imagePath: 'images',
                  color: AppThemeColor.white,
                  height: 20,
                  width: 20,
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: CommonText(
                    text: currentFolder != null
                        ? 'Save in "${currentFolder!.name}"'
                        : 'Save in "${selectedCollectionName}"',
                    textStyle: const TextStyle(
                      fontSize: 14,
                      color: AppThemeColor.white,
                    ),
                  ),
                ),
              ],
            ),
            onTap: () {
              if (currentFolder != null) {
                _saveRequest(selectedCollectionId!, currentFolder!.id.toString());
              } else {
                _saveRequest(selectedCollectionId!, null);
              }
            },
          ),
        ),

        const Divider(color: Colors.grey, height: 1),

        // Folders List with nested folder support
        Expanded(
          child: ListView.builder(
            itemCount: filteredFolders.length,
            itemBuilder: (context, index) {
              final folder = filteredFolders[index];
              return _buildFolderItem(folder);
            },
          ),
        ),
      ],
    );
  }


  void _showCreateCollectionDialog(BuildContext context) {
    final addCollectionController = TextEditingController();
    final descriptionController = TextEditingController();

    CommonAlertDialog.show(
      context: context,
      buttonCreateText: "Create",
      title: 'New Collection',
      description: 'Create a new collection to manage your API requests.',
      inputFields: [
        CommonTextFormField(
          labelText: "Enter Collection Name",
          hintTextString: "Enter Collection Name",
          isLabelText: true,
          isLabelTextBold: true,
          isValidationRequired: true,
          fontSize: 14,
          hintStyle: const TextStyle(color: AppThemeColor.hintTextColor),
          labelTextSize: Globals.labelTextSizeForDialog,
          textEditController: addCollectionController,
          cornerRadius: 4,
          errorMessage: "This field cannot be empty",
        ),
        const SizedBox(height: 16),
      ],
      onCancel: () {
        Navigator.of(context).pop();
      },
      onCreate: () {
        final collectionName = addCollectionController.text;
        final description = !isCheckEmpty(descriptionController.text)
            ? descriptionController.text
            : "Collection created on ${DateTime.now().toString().split('.')[0]}";

        if (!isCheckEmpty(collectionName)) {
          try {
            final collectionBloc = BlocProvider.of<CollectionBloc>(context);
            collectionBloc.add(
              CreateCollection(
                title: collectionName,
                description: description,
                workspaceId: int.parse(ApiConstant.cmsWorkspaceId),
              )
            );
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Creating collection...'),
                duration: Duration(seconds: 1),
              ),
            );
          } catch (e) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Error: ${e.toString()}')),
            );
          }
        }
        Navigator.of(context).pop();
      },
    );
  }
  // void _showCreateCollectionDialog() {
  //   final collectionNameController = TextEditingController();

  //   showDialog(
  //     context: context,
  //     builder: (context) => AlertDialog(
  //       backgroundColor: AppThemeColor.commonBackground,
  //       title: const Text('Create New Collection', style: TextStyle(color: AppThemeColor.white)),
  //       content: CommonTextFormField(
  //         labelText: "Collection Name",
  //         hintTextString: "Enter Collection Name",
  //         isLabelText: true,
  //         isLabelTextBold: true,
  //         fontSize: 14,
  //         hintStyle: const TextStyle(color: AppThemeColor.hintTextColor),
  //         labelTextSize: Globals.labelTextSizeForDialog,
  //         textEditController: collectionNameController,
  //         cornerRadius: 4,
  //         errorMessage: "",
  //       ),
  //       actions: [
  //         TextButton(
  //           onPressed: () => Navigator.of(context).pop(),
  //           child: const Text('Cancel', style: TextStyle(color: Colors.white70)),
  //         ),
  //         ElevatedButton(
  //           onPressed: () {
  //             if (collectionNameController.text.isNotEmpty) {
  //               context.read<CollectionBloc>().add(CreateCollection(
  //                 title: collectionNameController.text,
  //                 description: "Collection created on ${DateTime.now().toString().split('.')[0]}",
  //                 workspaceId: int.parse(ApiConstant.cmsWorkspaceId),
  //               ));
  //               Navigator.of(context).pop();
  //             }
  //           },
  //           child: const Text('Create'),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  void _showAddFolderDialog() {
    if (selectedFolderId != null) {
      final parentFolder = Folder(
        id: int.tryParse(selectedFolderId!),
        name: selectedFolderName,
        collectionId: int.tryParse(selectedCollectionId!),
      );
      _addFolderDialog(context, parentFolder: parentFolder);
    } else if (selectedCollectionId != null) {
      final collection = Collection(
        collectionId: int.tryParse(selectedCollectionId!),
        collectionTitle: selectedCollectionName,
      );
      _addFolderDialog(context, collection: collection);
    }
  }

  void _addFolderDialog(BuildContext context, {Collection? collection, Folder? parentFolder}) {
    final folderNameController = TextEditingController();
    final descriptionController = TextEditingController();

    CommonAlertDialog.show(
      context: context,
      buttonCreateText: "Create",
      title: 'Add Folder',
      description: 'Create a new folder to organize your API requests.',
      inputFields: [
        CommonTextFormField(
          labelText: "Enter Folder Name",
          hintTextString: "Enter Folder Name",
          isLabelText: true,
          isLabelTextBold: true,
          isValidationRequired: true,
          fontSize: 14,
          hintStyle: const TextStyle(color: AppThemeColor.hintTextColor),
          labelTextSize: Globals.labelTextSizeForDialog,
          textEditController: folderNameController,
          cornerRadius: 4,
          errorMessage: "This field cannot be empty",
        ),
      ],
      onCancel: () {
        Navigator.of(context).pop();
      },
      onCreate: () {
        final folderName = folderNameController.text;
        final description = !isCheckEmpty(descriptionController.text)
            ? descriptionController.text
            : "Folder created on ${DateTime.now().toString().split('.')[0]}";

        if (!isCheckEmpty(folderName)) {
          try {
            _addOptimisticFolder(folderName);
            final folderBloc = BlocProvider.of<FolderBloc>(context, listen: false);
            if (parentFolder != null) {
              folderBloc.add(
                CreateFolder(
                  name: folderName,
                  description: description,
                  parentFolderId: parentFolder.id?.toString(),
                )
              );
            } else {
              folderBloc.add(
                CreateFolder(
                name: folderName,
                description: description,
                collectionId: collection?.collectionId.toString(),
                )
              );
            }

            // Show a loading indicator
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Creating folder...'),
                duration: Duration(seconds: 1),
              ),
            );
          } catch (e) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Error: ${e.toString()}')),
            );
          }
        }
        Navigator.of(context).pop();
      },
    );
  }

  void _saveRequest(String collectionId, String? folderId) {
    print('collectionId: $collectionId ++++++++++ folderId: $folderId');

    if (folderId == null || folderId.isEmpty) {
      // saved in collection
      context.read<PostmanRequestBloc>().add(
        CreatePostmanRequest(
          name: widget.requestNameController.text,
          url: widget.activeTab.url ?? "",
          method: widget.activeTab.method,
          collectionId: collectionId,
          workspaceId: int.parse(ApiConstant.cmsWorkspaceId),
          isFromSaveDialog: true, // Flag to indicate this is from save dialog
        ),
      );
    } else {
      // saved in folder
      context.read<PostmanRequestBloc>().add(
        CreatePostmanRequest(
          name: widget.requestNameController.text,
          url: widget.activeTab.url ?? "",
          method: widget.activeTab.method,
          folderId: folderId,
          workspaceId: int.parse(ApiConstant.cmsWorkspaceId),
          isFromSaveDialog: true, // Flag to indicate this is from save dialog
        ),
      );
    }
  }

  // Build a folder item for navigation (no expansion, navigate into folder)
  Widget _buildFolderItem(Folder folder, {int indentLevel = 0}) {
    final hasSubfolders = folder.folders != null && folder.folders!.isNotEmpty;
    final isSelected = selectedFolderId == folder.id?.toString();

    return Material(
      type: MaterialType.transparency,
      child: Container(
        decoration: BoxDecoration(
          //color: isSelected ? Colors.blue.withValues(alpha: 0.2) : Colors.transparent,
          color: isSelected ? Colors.grey.withValues(alpha: 0.1) : Colors.transparent,
          borderRadius: BorderRadius.circular(4),
          //border: isSelected ? Border.all(color: Colors.blue, width: 1) : null,
        ),
        child: ListTile(
          hoverColor: Colors.grey.withValues(alpha: 0.1),
          contentPadding: const EdgeInsets.only(left: 8),
          visualDensity: const VisualDensity(horizontal: 0, vertical: -4),
        title: Row(
          children: [
            // Folder icon
            const CommonSVGIcon(
              imageName: 'folder2',
              imagePath: 'images',
              color: AppThemeColor.white,
              height: 20,
              width: 20,
            ),
            const SizedBox(width: 10),

            // Folder name
            Expanded(
              child: CommonText(
                text: folder.name ?? 'Unnamed Folder',
                textStyle: const TextStyle(
                  fontSize: 14,
                  color: AppThemeColor.white,
                ),
              ),
            ),

            // Show arrow if folder has subfolders
            if (hasSubfolders) ...[
              const CommonSVGIcon(
                imageName: 'direction_right',
                imagePath: 'images',
                color: AppThemeColor.white,
                height: 16,
                width: 16,
              ),
            ],
          ],
        ),
        onTap: () {
          if (hasSubfolders) {
            setState(() {
              breadcrumbPath.add(folder);
              currentFolder = folder;
              selectedFolderId = folder.id?.toString();
              selectedFolderName = folder.name;
              // Clear local cache when navigating to ensure fresh data
              _localFolderCache.clear();
              _hasLocalUpdates = false;
              _refreshKey++;
            });
            debugPrint('Navigated into folder: ${folder.name} with ${folder.folders?.length ?? 0} subfolders');
          } else {
            setState(() {
              selectedFolderId = folder.id?.toString();
              selectedFolderName = folder.name;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Selected "${folder.name}" folder. Click Save to save request here.'),
                duration: const Duration(seconds: 2),
                backgroundColor: Colors.green,
              ),
            );
          }
        },
        // onLongPress: () {
        //   // Long press to select folder for creating subfolders
        //   setState(() {
        //     selectedFolderId = folder.id.toString();
        //     selectedFolderName = folder.name;
        //   });
        //   ScaffoldMessenger.of(context).showSnackBar(
        //     SnackBar(
        //       content: Text('Selected "${folder.name}" for creating subfolders'),
        //       duration: const Duration(seconds: 2),
        //       backgroundColor: Colors.blue,
        //     ),
        //   );
        // },
        ),
      ),
    );
  }
}


class ScrollControllerProvider extends InheritedWidget {
  final ScrollController scrollControllerTabbar;

  const ScrollControllerProvider({
    Key? key,
    required this.scrollControllerTabbar,
    required Widget child,
  }) : super(key: key, child: child);

  static ScrollControllerProvider? of(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<ScrollControllerProvider>();
  }

  @override
  bool updateShouldNotify(ScrollControllerProvider oldWidget) {
    return oldWidget.scrollControllerTabbar != scrollControllerTabbar;
  }
}










