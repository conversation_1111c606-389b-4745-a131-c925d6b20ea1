import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:multi_split_view/multi_split_view.dart';
import 'package:postman_flutter/features/common_widgets/common_app_bar.dart';
import 'package:postman_flutter/features/home/<USER>/hive_db_provider.dart';
import 'package:postman_flutter/features/home/<USER>';
import 'package:uuid/uuid.dart';
import '../../globals.dart';
import '../../helpers/color_config.dart';
import '../../helpers/string_config.dart';
import '../../helpers/style_config.dart';
import '../common_widgets/CommonAlertDialog.dart';
import '../common_widgets/CommonButton.dart';
import '../common_widgets/CommonIconTextButton.dart';
import '../common_widgets/CommonSVGIcon.dart';
import '../common_widgets/CommonText.dart';
import '../common_widgets/CommonTextButton.dart';
import '../common_widgets/CommonTextFormField.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'history_panel.dart';
import 'bloc/home_bloc.dart';
import 'bloc/home_event.dart';
import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:multi_split_view/multi_split_view.dart';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'bloc/home_state.dart';
import 'collection_panel.dart';
import 'data/api_collection.dart';


enum AreaType { rightPanel, leftPanel }



class HomePage extends StatefulWidget {
  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final MultiSplitViewController _horizontalController = MultiSplitViewController();

  late HomeBloc homeBloc;
  @override
  void initState() {
    super.initState();
    homeBloc = BlocProvider.of<HomeBloc>(context,listen: false);
    _horizontalController.areas = [
      Area(data: AreaType.leftPanel, flex: 1, min: 0.8, max: 2),
      Area(data: AreaType.rightPanel, flex: 3),
    ];
    WidgetsBinding.instance.addPostFrameCallback((_){
      homeBloc.add(LoadApiDataEvent());
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppThemeColor.commonBackground,
      appBar: const CommonAppBar(),
      body: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Left Navigation Bar
          Container(
            width: 60,
            height: MediaQuery.of(context).size.height - 60,
            color: AppThemeColor.commonBackground,
            child: BlocBuilder<HomeBloc, HomeState>(
              builder: (context, state) {
                return Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      children: [
                        const SizedBox(height: 10,),
                        InkWell(
                          onTap: () {
                            homeBloc.add(SwitchToCollectionPanelEvent());
                          },
                          child: Container(
                            padding: const EdgeInsets.all(1),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              color: state.leftPanelType == LeftPanelType.collection?  AppThemeColor.lightBackground : null,
                            ),
                            child: const CommonSVGIcon(
                              imageName: 'collection',
                              imagePath: 'images',
                              height: 24,
                              width: 24,
                              color: Colors.white,
                              padding: EdgeInsets.all(8.0),
                            ),
                          ),
                        ),
                        const SizedBox(height: 10,),
                        InkWell(
                          onTap: (){
                            homeBloc.add(SwitchToHistoryPanelEvent());
                          },
                          child: Container(
                            padding: const EdgeInsets.all(1),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              color: state.leftPanelType == LeftPanelType.history?  AppThemeColor.lightBackground : null,
                            ),
                            child: const CommonSVGIcon(
                              imageName: 'history',
                              imagePath: 'images',
                              height: 24,
                              width: 24,
                              color: Colors.white,
                              padding: EdgeInsets.all(8.0),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const Column(
                      children: [
                        CommonSVGIcon(
                          imageName: 'logout',
                          imagePath: 'images',
                          height: 24,
                          width: 24,
                          padding: EdgeInsets.all(12.0),
                        ),
                        SizedBox(height: 50),
                      ],
                    ),
                  ],
                );
              },
            ),
          ),

          // Main Content Area
          Expanded(
            child: MultiSplitViewTheme(
              data: MultiSplitViewThemeData(),
              child: Container(
                color: AppThemeColor.lightBackground,
                child: BlocBuilder<HomeBloc, HomeState>(
                builder: (context, state) {
                  return MultiSplitView(
                    key: ValueKey(state.leftPanelType),
                    controller: _horizontalController,
                    builder: (context, area) {
                      if (area.data == AreaType.leftPanel) {
                        if (state.leftPanelType == LeftPanelType.collection) {
                          return const CollectionPanel();
                        } else {
                          return const HistoryPanel();
                        }
                      }
                      else if (area.data == AreaType.rightPanel) {
                        return Container(
                          color: AppThemeColor.commonBackground,
                          child: BlocBuilder<HomeBloc, HomeState>(
                            builder: (context, state) {
                              return TabView();
                            },
                          ),
                        );
                      }
                      return Container();
                    },
                  );
                },
               ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
