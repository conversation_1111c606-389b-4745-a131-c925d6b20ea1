import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../globals.dart';
import '../../helpers/color_config.dart';
import '../../helpers/style_config.dart';
import '../../model/history_model.dart';
import '../common_widgets/CommonAlertDialog.dart';
import '../common_widgets/CommonText.dart';
import '../common_widgets/CommonTextButton.dart';
import '../common_widgets/CommonTextFormField.dart';
import 'bloc/history/history_bloc.dart';
import 'bloc/history/history_event.dart';
import 'bloc/history/history_state.dart';
import 'bloc/home_bloc.dart';
import 'bloc/home_event.dart';
import 'data/api_collection.dart';

class HistoryPanel extends StatefulWidget {
  const HistoryPanel({super.key});

  @override
  State<HistoryPanel> createState() => _HistoryPanelState();
}

class _HistoryPanelState extends State<HistoryPanel> {
  late HomeBloc homeBloc;
  late HistoryBloc historyBloc;
  final searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    homeBloc = BlocProvider.of<HomeBloc>(context, listen: false);
    historyBloc = BlocProvider.of<HistoryBloc>(context, listen: false);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Load history data when the widget is first built
      historyBloc.add(const FetchHistory(
        userId: 1,
        //workspaceId: 1,
        workspaceId: 123,
        limit: 1000,
        page: 1,
      ));
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HistoryBloc, HistoryState>(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.only(left: 10.0),
          child: Column(
            children: [
              const SizedBox(height: 10),

              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CommonText(
                    text: 'History',
                    textStyle: mRegularTextStyle16(
                        textSize: 12, textColor: AppThemeColor.white),
                  ),

                  Row(
                    children: [
                      CommonTextButton(
                        text: 'Clear all',
                        enabled: state is HistoryLoaded && state.historyItems.isNotEmpty,
                        textStyle: mRegularTextStyle16(
                            textSize: 12, textColor: AppThemeColor.lightBlue),
                        callback: () {
                          _showClearHistory(context);
                        },
                      ),
                    ],
                  )
                ],
              ),

              const SizedBox(height: 5),

              // Search Input Field
              if (state is HistoryLoaded && state.historyItems.isNotEmpty)
                CommonTextFormField(
                  hintTextString: "Search",
                  isLabelText: false,
                  fontSize: 14,
                  padding: const EdgeInsets.only(right: 0, left: 0, top: 10, bottom: 10),
                  hintStyle: const TextStyle(color: AppThemeColor.hintTextColor),
                  textEditController: searchController,
                  cornerRadius: Globals.defaultTextInputCornerRadius,
                  isDense: true,
                  errorMessage: "",
                  onChange: (value) {
                    historyBloc.add(SearchHistory(query: value));
                  },
                ),

              const SizedBox(height: 10),

              // History content based on state
              Expanded(
                child: _buildHistoryContent(state),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHistoryContent(HistoryState state) {
    if (state is HistoryInitial) {
      return const Center(child: Text('Loading history...', style: TextStyle(color: Colors.white)));
    } else if (state is HistoryLoading) {
      return const Center(child: CircularProgressIndicator());
    } else if (state is HistoryError) {
      return Center(
        child: Text(
          'Error: ${state.message}',
          style: const TextStyle(color: Colors.red),
          textAlign: TextAlign.center,
        ),
      );
    } else if (state is HistoryLoaded) {
      if (state.filteredItems.isEmpty) {
        return const Center(
          child: Text(
            'No history items found',
            style: TextStyle(color: Colors.white70),
          ),
        );
      }

      // Group history items by date
      final groupedHistory = _groupHistoryItemsByDate(state.filteredItems);

      return ListView.builder(
        itemCount: groupedHistory.length,
        padding: EdgeInsets.zero,
        itemBuilder: (context, index) {
          final entry = groupedHistory.entries.elementAt(index);
          final date = entry.key;
          final historyItems = entry.value;

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: CommonText(
                  text: date,
                  textStyle: mMediumTextStyle16(
                    textSize: 14,
                    textColor: AppThemeColor.white,
                  ),
                ),
              ),
              ...historyItems.map((item) => _buildHistoryItem(item)),
            ],
          );
        },
      );
    } else if (state is HistoryCleared) {
      return Center(
        child: Text(
          state.message,
          style: const TextStyle(color: Colors.green),
          textAlign: TextAlign.center,
        ),
      );
    } else {
      return const Center(child: Text('Unknown state', style: TextStyle(color: Colors.white)));
    }
  }

  Map<String, List<HistoryItem>> _groupHistoryItemsByDate(List<HistoryItem> historyItems) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    final groupedHistory = <String, List<HistoryItem>>{};

    for (final item in historyItems) {
      try {
        // Parse the date safely
        DateTime requestDate;
        try {
          requestDate = DateTime.parse(item.createdAt);
        } catch (e) {
          // If parsing fails, use current date
          requestDate = now;
        }

        final requestDay = DateTime(requestDate.year, requestDate.month, requestDate.day);

        String dateLabel;
        if (requestDay == today) {
          dateLabel = 'Today';
        } else if (requestDay == yesterday) {
          dateLabel = 'Yesterday';
        } else {
          dateLabel = '${_getMonthName(requestDay.month)} ${requestDay.day}, ${requestDay.year}';
        }

        if (!groupedHistory.containsKey(dateLabel)) {
          groupedHistory[dateLabel] = [];
        }
        groupedHistory[dateLabel]!.add(item);
      } catch (e) {
        // If any error occurs, add to "Unknown Date" group
        const dateLabel = 'Unknown Date';
        if (!groupedHistory.containsKey(dateLabel)) {
          groupedHistory[dateLabel] = [];
        }
        groupedHistory[dateLabel]!.add(item);
      }
    }

    return groupedHistory;
  }

  String _getMonthName(int month) {
    switch (month) {
      case 1:
        return 'January';
      case 2:
        return 'February';
      case 3:
        return 'March';
      case 4:
        return 'April';
      case 5:
        return 'May';
      case 6:
        return 'June';
      case 7:
        return 'July';
      case 8:
        return 'August';
      case 9:
        return 'September';
      case 10:
        return 'October';
      case 11:
        return 'November';
      case 12:
        return 'December';
      default:
        return '';
    }
  }

  Widget _buildHistoryItem(HistoryItem item) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const SizedBox(width: 10),

        Expanded(
          child: InkWell(
            onTap: () {
              try {
                // Open the request in a new tab
                final Map<String, dynamic> requestData;

                if (item.response.containsKey('request') && item.response['request'] is Map) {
                  requestData = Map<String, dynamic>.from(item.response['request']);
                } else {
                  requestData = {}; // Default empty map if request data is missing or invalid
                }

                // Create a request object to open in a new tab
                homeBloc.add(OpenTabEvent(
                  tabModel: TabModel(
                    uuid: item.apiRequestHistoryId.toString(),
                    tabName: item.apiRequestName,
                    method: item.apiRequestMethod,
                    url: item.apiRequestUrl,
                    jsonData: requestData.containsKey('body') && requestData['body'] is Map
                        ? Map<String, dynamic>.from(requestData['body'])
                        : {},
                  ),
                ));
              } catch (e) {
                // Show error message
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Error opening request: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 5),
                CommonText(
                  text: item.apiRequestName,
                  textStyle: mRegularTextStyle16(
                    textSize: 14,
                    textColor: AppThemeColor.white,
                  ),
                ),
                CommonText(
                  text: '${item.apiRequestMethod} ${item.apiRequestUrl}',
                  textStyle: mRegularTextStyle16(
                    textSize: 12,
                    textColor: AppThemeColor.tabUnselectedTextColor,
                  ),
                ),
                const SizedBox(height: 5),
              ],
            ),
          ),
        ),

        IconButton(
          icon: const Icon(
            Icons.delete,
            size: 20,
            color: AppThemeColor.tabUnselectedTextColor,
          ),
          onPressed: () {
            // Show delete confirmation dialog
            CommonDeleteAlertDialog.show(
              context: context,
              buttonCreateText: "Delete",
              title: 'Delete History Item',
              description: 'Are you sure you want to delete this history item?',
              inputFields: [],
              onCancel: () {
                Navigator.of(context).pop();
              },
              onCreate: () {
                // TODO: Implement delete single history item API
                Navigator.of(context).pop();
              },
            );
          },
        ),
      ],
    );
  }

  void _showClearHistory(BuildContext context) {
    CommonDeleteAlertDialog.show(
      context: context,
      buttonCreateText: "Clear",
      title: 'Clear History',
      description: 'Are you sure you want to clear all history? This action cannot be undone.',
      inputFields: [],
      onCancel: () {
        Navigator.of(context).pop();
      },
      onCreate: () {
        historyBloc.add(const ClearHistory(
          userId: 1,
          workspaceId: 1,
        ));
        Navigator.of(context).pop();
      },
    );
  }
}