
import 'package:equatable/equatable.dart';

import 'query_params_model.dart';


import 'package:equatable/equatable.dart';

abstract class QueryParamsEvent extends Equatable {
  @override
  List<Object> get props => [];
}

class AddQueryParamsRowEvent extends QueryParamsEvent {}

class UpdateQueryParamsRowEvent extends QueryParamsEvent {
  final int rowIndex;
  final String key;
  final String value;
  final String description;

  UpdateQueryParamsRowEvent({
    required this.rowIndex,
    required this.key,
    required this.value,
    required this.description,
  });

  @override
  List<Object> get props => [rowIndex, key, value, description];
}

class QueryParamsToggleRowSelectionEvent extends QueryParamsEvent {
  final int rowIndex;
  final bool isSelected;

  QueryParamsToggleRowSelectionEvent({
    required this.rowIndex,
    required this.isSelected,
  });

  @override
  List<Object> get props => [rowIndex, isSelected];
}


class ClearQueryParamsEvent extends QueryParamsEvent {}

// class SyncQueryParamsEvent extends QueryParamsEvent {
//   final Map<String, String?> queryParams;
//
//   SyncQueryParamsEvent(this.queryParams);
//
//   @override
//   List<Object> get props => [queryParams];
// }





/*abstract class QueryParamsEvent extends Equatable {
  @override
  List<Object> get props => [];
}

class AddQueryParamsRowEvent extends QueryParamsEvent {}

class UpdateQueryParamsRowEvent extends QueryParamsEvent {
  final int rowIndex;
  final String key;
  final String value;
  final String description;

  UpdateQueryParamsRowEvent({
    required this.rowIndex,
    required this.key,
    required this.value,
    required this.description,
  });

  @override
  List<Object> get props => [rowIndex, key, value, description];
}

class QueryParamsToggleRowSelectionEvent extends QueryParamsEvent {
  final int rowIndex;
  final bool isSelected;

  QueryParamsToggleRowSelectionEvent({required this.rowIndex, required this.isSelected});

  @override
  List<Object> get props => [rowIndex, isSelected];
}*/





