import 'package:bloc/bloc.dart';
import 'package:flutter/cupertino.dart';

import 'query_params_event.dart';
import 'query_params_model.dart';
import 'query_params_state.dart';


class QueryParamsBloc extends Bloc<QueryParamsEvent, QueryParamsState> {
  QueryParamsBloc() : super(QueryParamsState(rows: [])) {

    // Add a new row to the query params
    on<AddQueryParamsRowEvent>((event, emit) {
      final newRow = QueryParamsRow(
        keyController: TextEditingController(),
        valueController: TextEditingController(),
        descriptionController: TextEditingController(),
      );
      final newRows = List<QueryParamsRow>.from(state.rows)..add(newRow);
      emit(state.copyWith(rows: newRows));
    });

    // Update a row in the query params
    on<UpdateQueryParamsRowEvent>((event, emit) {
      final updatedRows = List<QueryParamsRow>.from(state.rows);
      final row = updatedRows[event.rowIndex];

      if (!row.isStatic || event.key == row.key) {
        updatedRows[event.rowIndex] = row.copyWith(
          key: event.key,
          value: event.value,
          description: event.description,
        );
      }

      emit(state.copyWith(rows: updatedRows));

      // Automatically add a new row when the last row is updated
      if (event.rowIndex == updatedRows.length - 1) {
        add(AddQueryParamsRowEvent());
      }
    });
    // Toggle the selection of a row
    on<QueryParamsToggleRowSelectionEvent>((event, emit) {
      final updatedRows = List<QueryParamsRow>.from(state.rows);
      updatedRows[event.rowIndex] =
          updatedRows[event.rowIndex].copyWith(isSelected: event.isSelected);
      emit(state.copyWith(rows: updatedRows));
    });

    on<ClearQueryParamsEvent>((event, emit) {
      print("Handling ClearQueryParamsEvent");
      final clearedRows = state.rows.map((row) {
        print("Clearing row: key=${row.key}, value=${row.value}, description=${row.description}");
        row.keyController.clear();
        row.valueController.clear();
        row.descriptionController.clear();

        return row.copyWith(
          key: '',
          value: '',
          description: '',
        );
      }).toList();

      emit(state.copyWith(rows: clearedRows));
    });

    // on<SyncQueryParamsEvent>((event, emit) {
    //   final updatedRows = state.rows.map((row) {
    //     // Check if the key exists in the new query parameters
    //     final newValue = event.queryParams[row.key];
    //
    //     if (newValue == null) {
    //       // Key has been removed or cleared, reset the row
    //       row.keyController.clear();
    //       row.valueController.clear();
    //       row.descriptionController.clear();
    //       return row.copyWith(key: '', value: '', description: '');
    //     } else {
    //       // Update the value if the key still exists
    //       row.valueController.text = newValue;
    //       return row.copyWith(value: newValue);
    //     }
    //   }).toList();
    //
    //   emit(state.copyWith(rows: updatedRows));
    // });

  }
}





/*class QueryParamsBloc extends Bloc<QueryParamsEvent, QueryParamsState> {
  QueryParamsBloc()
      : super(QueryParamsState(rows: [])) {
    on<AddQueryParamsRowEvent>((event, emit) {
      final newRow = QueryParamsRow(
        keyController: TextEditingController(),
        valueController: TextEditingController(),
        descriptionController: TextEditingController(),
      );
      final newRows = List<QueryParamsRow>.from(state.rows)..add(newRow);
      emit(state.copyWith(rows: newRows));
    });

    on<UpdateQueryParamsRowEvent>((event, emit) {
      final updatedRows = List<QueryParamsRow>.from(state.rows);
      final row = updatedRows[event.rowIndex];


      if (!row.isStatic || event.key == row.key) {
        updatedRows[event.rowIndex] = row.copyWith(
          key: event.key,
          value: event.value,
          description: event.description,
        );
      }

      emit(state.copyWith(rows: updatedRows));

      if (event.rowIndex == updatedRows.length - 1) {
        add(AddQueryParamsRowEvent());
      }
    });

    on<QueryParamsToggleRowSelectionEvent>((event, emit) {
      final updatedRows = List<QueryParamsRow>.from(state.rows);
      updatedRows[event.rowIndex] =
          updatedRows[event.rowIndex].copyWith(isSelected: event.isSelected);
      emit(state.copyWith(rows: updatedRows));
    });


  }


}*/






