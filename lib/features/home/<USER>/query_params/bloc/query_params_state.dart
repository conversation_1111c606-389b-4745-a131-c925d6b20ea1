import 'query_params_model.dart';
import 'package:equatable/equatable.dart';

class QueryParamsState extends Equatable {
  final List<QueryParamsRow> rows;

  const QueryParamsState({required this.rows});

  QueryParamsState copyWith({List<QueryParamsRow>? rows}) {
    return QueryParamsState(rows: rows ?? this.rows);
  }

  @override
  List<Object?> get props => [rows];
}



extension QueryParamsStateExtension on QueryParamsState {
  /// Generates the query string based on selected rows
  String generateQueryString() {
    final params = rows
        .where((row) => row.isSelected && (row.key.isNotEmpty || row.value.isNotEmpty))
        .map((row) {
      // If key is empty, return `=value`
      if (row.key.isEmpty) {
        return '=${Uri.encodeComponent(row.value)}';
      }
      // If value is empty, return only the key
      if (row.value.isEmpty) {
        return Uri.encodeComponent(row.key);
      }
      // Otherwise, return `key=value`
      return '${Uri.encodeComponent(row.key)}=${Uri.encodeComponent(row.value)}';
    })
        .join('&');
    return params.isNotEmpty ? '?$params' : '';
  }
}










/*class QueryParamsState extends Equatable {
  final List<QueryParamsRow> rows;

  const QueryParamsState({required this.rows});

  QueryParamsState copyWith({List<QueryParamsRow>? rows}) {
    return QueryParamsState(rows: rows ?? this.rows);
  }

  @override
  List<Object?> get props => [rows];

}*/

