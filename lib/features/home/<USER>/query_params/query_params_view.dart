import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/query_params/bloc/query_params_state.dart';

import '../../../../../helpers/color_config.dart';
import '../../../../../helpers/style_config.dart';
import '../../../common_widgets/CommonText.dart';
import '../../../common_widgets/custom_checkbox.dart';
import 'bloc/query_params_bloc.dart';
import 'bloc/query_params_event.dart';

class QueryParamsView extends StatefulWidget {
  const QueryParamsView({super.key});

  @override
  State<QueryParamsView> createState() => _QueryParamsViewState();
}

class _QueryParamsViewState extends State<QueryParamsView> {

  int? hoveredColumnIndex;
  bool isDragging = false;
  List<double> columnWidths = [1.0, 1.0, 2.0];

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<QueryParamsBloc, QueryParamsState>(
      builder: (context, state) {
        return Padding(
          padding: EdgeInsets.only(right: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.only(bottom: 10, top: 5),
                child: CommonText(
                  text: "Query Params",
                  textStyle: mMediumTextStyle16(
                      textSize: 14, textColor: AppThemeColor.white),
                ),
              ),

              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.vertical,
                  child: Table(
                    border: TableBorder.all(
                        color: AppThemeColor.commonBorderColor),
                    columnWidths: {
                      0: const FixedColumnWidth(40),
                      1: FlexColumnWidth(columnWidths[0]),
                      2: FlexColumnWidth(columnWidths[1]),
                      3: FlexColumnWidth(columnWidths[2]),
                    },
                    defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                    children: [
                      _buildHeaderRow(),
                      ..._buildDataRows(context, state),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  TableRow _buildHeaderRow() {
    return TableRow(
      children: [
        Container(),
        _buildResizableHeaderCell('Key', 0),
        _buildResizableHeaderCell('Value', 1),
        _buildResizableHeaderCell('Description', 2),
      ],
    );
  }

  Widget _buildResizableHeaderCell(String text, int columnIndex) {
    return MouseRegion(
      onEnter: (_) {
        setState(() {
          hoveredColumnIndex = columnIndex;
        });
      },
      onExit: (_) {
        setState(() {
          hoveredColumnIndex = null;
        });
      },
      child: Stack(
        children: [
          Container(
            height: 35,
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.symmetric(horizontal: 12.0),
            color: AppThemeColor.commonBackground,
            child: CommonText(
              text: text,
              textAlign: TextAlign.left,
              textStyle: mMediumTextStyle16(
                  textSize: 14, textColor: AppThemeColor.tableHeaderTextColor),
            ),
          ),
          if (hoveredColumnIndex == columnIndex || isDragging)
            Positioned(
              right: -10,
              top: 0,
              bottom: 0,
              child: GestureDetector(
                onHorizontalDragStart: (_) {
                  setState(() {
                    isDragging = true;
                  });
                },
                onHorizontalDragUpdate: (details) {
                  setState(() {
                    columnWidths[columnIndex] += details.primaryDelta! * 0.01;
                    columnWidths[columnIndex] =
                        columnWidths[columnIndex].clamp(0.5, 5.0);
                  });
                },
                onHorizontalDragEnd: (_) {
                  setState(() {
                    isDragging = false;
                  });
                },
                child: MouseRegion(
                  cursor: SystemMouseCursors.resizeColumn,
                  child: Container(
                    width: 20,
                    alignment: Alignment.center,
                    child: Icon(
                      Icons.drag_handle,
                      size: 16,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }


  List<TableRow> _buildDataRows(BuildContext context, QueryParamsState state) {
    return List.generate(state.rows.length, (index) {
      final row = state.rows[index];
      return TableRow(
        children: [
          _buildCheckboxCell(context, index, row.isSelected),
          _buildTextFieldCell(
              context, index, row.keyController, 'key', isStatic: row.isStatic),
          _buildTextFieldCell(context, index, row.valueController, 'value'),
          _buildTextFieldCell(
              context, index, row.descriptionController, 'description'),
        ],
      );
    });
  }

  Widget _buildTextFieldCell(BuildContext context,
      int rowIndex,
      TextEditingController controller,
      String fieldName, {
        bool isStatic = false,
      }) {
    return Container(
      height: 35,
      padding: const EdgeInsets.symmetric(horizontal: 12.0),
      alignment: Alignment.centerLeft,
      child: TextField(
        controller: controller,
        textAlign: TextAlign.left,
        style: TextStyle(
            color: AppThemeColor.tableHeaderTextColor, fontSize: 12),
        decoration: InputDecoration(
          contentPadding: EdgeInsets.zero,
          border: const OutlineInputBorder(
            borderSide: BorderSide.none,
          ),
          hintText: fieldName,
          hintStyle: TextStyle(
              color: AppThemeColor.tableTextColor, fontSize: 12),
        ),
        onChanged: (value) {
          final queryParamsBloc = BlocProvider.of<QueryParamsBloc>(context);
          final row = queryParamsBloc.state.rows[rowIndex];

          queryParamsBloc.add(
            UpdateQueryParamsRowEvent(
              rowIndex: rowIndex,
              key: fieldName == 'key' ? value : row.key,
              value: fieldName == 'value' ? value : row.value,
              description: fieldName == 'description' ? value : row.description,
            ),
          );
        },
      ),
    );
  }

  Widget _buildCheckboxCell(BuildContext context, int rowIndex,
      bool isSelected) {
    return Center(
      child: CustomCheckbox(
        value: isSelected,
        label: "",
        checkboxSize: 20,
        checkIconSize: 14,
        onChanged: (value) {
          BlocProvider.of<QueryParamsBloc>(context).add(
            QueryParamsToggleRowSelectionEvent(rowIndex: rowIndex, isSelected: value!),
          );
        },
      ),
    );
  }

}

