part of 'folder_bloc.dart';

sealed class FolderState extends Equatable {
  const FolderState();

  @override
  List<Object?> get props => [];
}

final class FolderInitial extends FolderState {}

// Create folder states
class FolderCreating extends FolderState {}

class FolderCreated extends FolderState {
  final String message;
  final FolderModel newFolder;

  const FolderCreated(this.message, this.newFolder);

  @override
  List<Object> get props => [message, newFolder];
}

class FolderCreationError extends FolderState {
  final String message;

  const FolderCreationError(this.message);

  @override
  List<Object> get props => [message];
}

// Update folder states
class FolderUpdating extends FolderState {}

class FolderUpdated extends FolderState {
  final String message;
  final FolderModel updatedFolder;

  const FolderUpdated(this.message, this.updatedFolder);

  @override
  List<Object> get props => [message, updatedFolder];
}

class FolderUpdateError extends FolderState {
  final String message;

  const FolderUpdateError(this.message);

  @override
  List<Object> get props => [message];
}

// Delete folder states
class FolderDeleting extends FolderState {}

class FolderDeleted extends FolderState {
  final String message;

  const FolderDeleted(this.message);

  @override
  List<Object> get props => [message];
}

class FolderDeleteError extends FolderState {
  final String message;

  const FolderDeleteError(this.message);

  @override
  List<Object> get props => [message];
}
