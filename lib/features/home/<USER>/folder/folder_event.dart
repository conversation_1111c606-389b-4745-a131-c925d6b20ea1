part of 'folder_bloc.dart';

sealed class FolderEvent extends Equatable {
  const FolderEvent();

  @override
  List<Object?> get props => [];
}

class CreateFolder extends FolderEvent {
  final String name;
  final String description;
  final String? collectionId;
  final String? parentFolderId;

  const CreateFolder({
    required this.name,
    required this.description,
    this.collectionId,
    this.parentFolderId,
  });

  @override
  List<Object?> get props => [name, description, collectionId, parentFolderId];
}

class UpdateFolder extends FolderEvent {
  final int folderId;
  final String name;
  final String description;
  final String collectionId;
  final String? parentFolderId;

  const UpdateFolder({
    required this.folderId,
    required this.name,
    required this.description,
    required this.collectionId,
    this.parentFolderId,
  });

  @override
  List<Object?> get props => [folderId, name, description, collectionId, parentFolderId];
}

class DeleteFolder extends FolderEvent {
  final int folderId;

  const DeleteFolder({
    required this.folderId,
  });

  @override
  List<Object> get props => [folderId];
}
