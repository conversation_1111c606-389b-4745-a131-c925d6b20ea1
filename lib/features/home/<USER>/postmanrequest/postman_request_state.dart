import 'package:equatable/equatable.dart';
import 'package:postman_flutter/model/request_details_model.dart';
import 'package:postman_flutter/model/request_model.dart';


abstract class PostmanRequestState extends Equatable {
  const PostmanRequestState();

  @override
  List<Object?> get props => [];
}

class PostmanRequestInitial extends PostmanRequestState {}

class PostmanRequestCreating extends PostmanRequestState {}

class PostmanRequestCreated extends PostmanRequestState {
  final RequestModel request;
  final String message;
  final int? collectionId;
  final int? folderId;
  final bool isFromSaveDialog; 

  const PostmanRequestCreated({
    required this.request,
    required this.message,
    this.collectionId,
    this.folderId,
    this.isFromSaveDialog = false,
  });

  @override
  List<Object?> get props => [request, message, collectionId, folderId, isFromSaveDialog];
}

class PostmanRequestCreationError extends PostmanRequestState {
  final String message;

  const PostmanRequestCreationError({
    required this.message,
  });

  @override
  List<Object?> get props => [message];
}

class PostmanRequestFetching extends PostmanRequestState {}

class PostmanRequestFetched extends PostmanRequestState {
  final RequestModel request;
  final String message;
  final String? collectionName;
  final String? folderName;

  const PostmanRequestFetched({
    required this.request,
    required this.message,
    this.collectionName,
    this.folderName,
  });

  @override
  List<Object?> get props => [request, message, collectionName, folderName];
}

class PostmanRequestFetchError extends PostmanRequestState {
  final String message;

  const PostmanRequestFetchError({
    required this.message,
  });

  @override
  List<Object?> get props => [message];
}

class SendingDirectRequest extends PostmanRequestState {}

class DirectRequestSent extends PostmanRequestState {
  final Map<String, dynamic> response;
  final String tabUuid;

  const DirectRequestSent({
    required this.response,
    required this.tabUuid,
  });

  @override
  List<Object?> get props => [response, tabUuid];
}

class DirectRequestError extends PostmanRequestState {
  final String message;
  final String tabUuid;

  const DirectRequestError({
    required this.message,
    required this.tabUuid,
  });

  @override
  List<Object?> get props => [message, tabUuid];
}

class SavingRequest extends PostmanRequestState {}

class RequestSaved extends PostmanRequestState {
  final Map<String, dynamic> response;
  final String tabUuid;

  const RequestSaved({
    required this.response,
    required this.tabUuid,
  });

  @override
  List<Object?> get props => [response, tabUuid];
}

class RequestSaveError extends PostmanRequestState {
  final String message;
  final String tabUuid;

  const RequestSaveError({
    required this.message,
    required this.tabUuid,
  });

  @override
  List<Object?> get props => [message, tabUuid];
}

class FetchingRequestDetails extends PostmanRequestState {}

class RequestDetailsFetched extends PostmanRequestState {
  final RequestDetailsModel requestDetails;
  final String message;
  final String? collectionName;
  final String? folderName;

  const RequestDetailsFetched({
    required this.requestDetails,
    required this.message,
    this.collectionName,
    this.folderName,
  });

  @override
  List<Object?> get props => [requestDetails, message, collectionName, folderName];
}

class RequestDetailsFetchError extends PostmanRequestState {
  final String message;

  const RequestDetailsFetchError({
    required this.message,
  });

  @override
  List<Object?> get props => [message];
}

class RenamingRequest extends PostmanRequestState {}

class RequestRenamed extends PostmanRequestState {
  final Map<String, dynamic> response;
  final String message;

  const RequestRenamed({
    required this.response,
    required this.message,
  });

  @override
  List<Object?> get props => [response, message];
}

class RequestRenameError extends PostmanRequestState {
  final String message;

  const RequestRenameError({
    required this.message,
  });

  @override
  List<Object?> get props => [message];
}

class DuplicatingRequest extends PostmanRequestState {}

class RequestDuplicated extends PostmanRequestState {
  final Map<String, dynamic> response;
  final String message;

  const RequestDuplicated({
    required this.response,
    required this.message,
  });

  @override
  List<Object?> get props => [response, message];
}

class RequestDuplicateError extends PostmanRequestState {
  final String message;

  const RequestDuplicateError({
    required this.message,
  });

  @override
  List<Object?> get props => [message];
}

class DeletingRequest extends PostmanRequestState {}

class RequestDeleted extends PostmanRequestState {
  final Map<String, dynamic> response;
  final String message;

  const RequestDeleted({
    required this.response,
    required this.message,
  });

  @override
  List<Object?> get props => [response, message];
}

class RequestDeleteError extends PostmanRequestState {
  final String message;

  const RequestDeleteError({
    required this.message,
  });

  @override
  List<Object?> get props => [message];
}
