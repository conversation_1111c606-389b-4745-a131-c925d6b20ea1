import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/history/history_event.dart';
import 'package:postman_flutter/features/home/<USER>/history/history_state.dart';
import 'package:postman_flutter/repo/history_repo.dart';

class HistoryBloc extends Bloc<HistoryEvent, HistoryState> {
  final HistoryRepository repository;

  HistoryBloc({required this.repository}) : super(HistoryInitial()) {
    on<FetchHistory>(_onFetchHistory);
    on<ClearHistory>(_onClearHistory);
    on<SearchHistory>(_onSearchHistory);
  }

  Future<void> _onFetchHistory(
    FetchHistory event,
    Emitter<HistoryState> emit,
  ) async {
    try {
      emit(HistoryLoading());

      debugPrint('Fetching history for user: ${event.userId}, workspace: ${event.workspaceId}');

      final response = await repository.getHistory(
        userId: event.userId,
        workspaceId: event.workspaceId,
        limit: event.limit,
        page: event.page,
      );

      if (response.status.toLowerCase() == 'success' && response.data != null) {
        try {
          debugPrint('History fetched successfully: ${response.data!.history.length} items');

          emit(HistoryLoaded(
            historyItems: response.data!.history,
            total: response.data!.total,
            totalPages: response.data!.totalPages,
          ));
        } catch (e) {
          debugPrint('Error processing history data: $e');
          // If there's an error processing the data, emit an empty list
          emit(const HistoryLoaded(
            historyItems: [],
            total: 0,
            totalPages: 0,
          ));
        }
      } else {
        debugPrint('Failed to fetch history: ${response.msg}');
        emit(HistoryError(message: response.msg));
      }
    } catch (e) {
      debugPrint('Error fetching history: $e');
      emit(HistoryError(message: 'Error fetching history: $e'));
    }
  }

  Future<void> _onClearHistory(
    ClearHistory event,
    Emitter<HistoryState> emit,
  ) async {
    try {
      emit(HistoryLoading());

      debugPrint('Clearing history for user: ${event.userId}, workspace: ${event.workspaceId}');

      final response = await repository.clearHistory(
        userId: event.userId,
        workspaceId: event.workspaceId,
      );

      if (response['status'].toLowerCase() == 'success') {
        debugPrint('History cleared successfully');
        emit(HistoryCleared(message: response['msg'] ?? 'History cleared successfully'));

        // Fetch empty history after clearing
        add(FetchHistory(
          userId: event.userId,
          workspaceId: event.workspaceId,
        ));
      } else {
        debugPrint('Failed to clear history: ${response['msg']}');
        emit(HistoryError(message: response['msg'] ?? 'Failed to clear history'));
      }
    } catch (e) {
      debugPrint('Error clearing history: $e');
      emit(HistoryError(message: 'Error clearing history: $e'));
    }
  }

  void _onSearchHistory(
    SearchHistory event,
    Emitter<HistoryState> emit,
  ) {
    if (state is HistoryLoaded) {
      final currentState = state as HistoryLoaded;
      emit(currentState.copyWith(searchQuery: event.query));
    }
  }
}
