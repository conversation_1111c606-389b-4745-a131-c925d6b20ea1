import 'package:equatable/equatable.dart';

abstract class HistoryEvent extends Equatable {
  const HistoryEvent();

  @override
  List<Object?> get props => [];
}

class FetchHistory extends HistoryEvent {
  final int userId;
  final int workspaceId;
  final int limit;
  final int page;

  const FetchHistory({
    required this.userId,
    required this.workspaceId,
    this.limit = 10,
    this.page = 1,
  });

  @override
  List<Object?> get props => [userId, workspaceId, limit, page];
}

class ClearHistory extends HistoryEvent {
  final int userId;
  final int workspaceId;

  const ClearHistory({
    required this.userId,
    required this.workspaceId,
  });

  @override
  List<Object?> get props => [userId, workspaceId];
}

class SearchHistory extends HistoryEvent {
  final String query;

  const SearchHistory({required this.query});

  @override
  List<Object?> get props => [query];
}
