import 'package:equatable/equatable.dart';
import 'package:postman_flutter/model/history_model.dart';

abstract class HistoryState extends Equatable {
  const HistoryState();

  @override
  List<Object?> get props => [];
}

class HistoryInitial extends HistoryState {}

class HistoryLoading extends HistoryState {}

class HistoryLoaded extends HistoryState {
  final List<HistoryItem> historyItems;
  final int total;
  final int totalPages;
  final String searchQuery;

  const HistoryLoaded({
    required this.historyItems,
    required this.total,
    required this.totalPages,
    this.searchQuery = '',
  });

  @override
  List<Object?> get props => [historyItems, total, totalPages, searchQuery];

  HistoryLoaded copyWith({
    List<HistoryItem>? historyItems,
    int? total,
    int? totalPages,
    String? searchQuery,
  }) {
    return HistoryLoaded(
      historyItems: historyItems ?? this.historyItems,
      total: total ?? this.total,
      totalPages: totalPages ?? this.totalPages,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }

  List<HistoryItem> get filteredItems {
    if (searchQuery.isEmpty) return historyItems;
    
    return historyItems.where((item) =>
      item.apiRequestName.toLowerCase().contains(searchQuery.toLowerCase()) ||
      item.apiRequestUrl.toLowerCase().contains(searchQuery.toLowerCase()) ||
      item.apiRequestMethod.toLowerCase().contains(searchQuery.toLowerCase())
    ).toList();
  }
}

class HistoryError extends HistoryState {
  final String message;

  const HistoryError({required this.message});

  @override
  List<Object?> get props => [message];
}

class HistoryCleared extends HistoryState {
  final String message;

  const HistoryCleared({required this.message});

  @override
  List<Object?> get props => [message];
}
