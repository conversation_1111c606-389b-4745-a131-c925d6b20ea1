import 'package:flutter/material.dart';

import '../../../../helpers/color_config.dart';
import '../../../../helpers/style_config.dart';
import '../../../common_widgets/CommonText.dart';

class InheritAuthFromParentAdditional extends StatefulWidget {
  const InheritAuthFromParentAdditional({super.key});

  @override
  State<InheritAuthFromParentAdditional> createState() => _InheritAuthFromParentAdditionalState();
}

class _InheritAuthFromParentAdditionalState extends State<InheritAuthFromParentAdditional> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(top: 10),
      child: CommonText(
        text: 'The authorization header will be automatically generated when you send the request. Learn more about authorization.',
        maxLine: 1000,
        textStyle: mRegularTextStyle16(
            textSize: 12, textColor: AppThemeColor.tableTextColor),
      ),
    );
  }
}
