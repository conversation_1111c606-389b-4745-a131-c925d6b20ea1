import '../api_key/api_key_data.dart';
import '../basic_auth/basic_auth.dart';
import '../basic_auth/basic_auth_data.dart';
import '../bearer_token/bearer_token_data.dart';
import '../jwt_bearer/jwt_bearer_data.dart';



abstract class AuthSubViewsEvent {}

class AuthTypeChanged extends AuthSubViewsEvent {
  final String authType;
  AuthTypeChanged({required this.authType});
}

class BasicAuthFieldsChanged extends AuthSubViewsEvent {
  final BasicAuthFields bsicAuthFields;
  BasicAuthFieldsChanged({required this.bsicAuthFields});
}

class BearerTokenFieldsChanged extends AuthSubViewsEvent {
  final BearerTokenFields bearerTokenFields;
  BearerTokenFieldsChanged({required this.bearerTokenFields});
}


class ApiKeyFieldsChanged extends AuthSubViewsEvent {
  final ApiKeyFields apiKeyFields;
  ApiK<PERSON>FieldsChanged({required this.apiKeyFields});
}

class JwtBearerFieldsChanged extends AuthSubViewsEvent {
  final JwtBearerFields jwtBearerFields;
  JwtBearerFieldsChanged({required this.jwtBearerFields});
}




