import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../api_key/api_key.dart';
import '../api_key/api_key_additional.dart';
import '../basic_auth/basic_auth.dart';
import '../basic_auth/basic_auth_additional.dart';
import '../basic_auth/basic_auth_data.dart';
import '../bearer_token/bearer_token.dart';
import '../bearer_token/bearer_token_additional.dart';
import '../inherit_auth_from_parent.dart';
import '../inherit_auth_from_parent_additional.dart';
import '../jwt_bearer/jwt_bearer.dart';
import '../jwt_bearer/jwt_bearer_additional.dart';
import '../no_auth.dart';
import '../no_auth_additional.dart';
import 'authorization_event.dart';
import 'authorization_state.dart';

/*class AuthorizationBloc extends Bloc<AuthorizationEvent, AuthorizationState> {

  AuthorizationBloc()
      : super(
    AuthorizationState(
      selectedAuthType: 'Inherit auth from parent',
      primaryView: InheritAuthFromParent(),
      secondaryView: InheritAuthFromParentAdditional(),
    ),
  ) {
    on<AuthTypeChanged>(_onAuthTypeChanged);
   // on<AuthFieldsChanged>(_onUpdateBasicAuthData);
  }

  void _onAuthTypeChanged(AuthTypeChanged event, Emitter<AuthorizationState> emit) {
    final dynamicViews = _getDynamicViews();
    final selectedType = event.selectedAuthType;
    final primaryView = dynamicViews[selectedType]?['primaryView'];
    final secondaryView = dynamicViews[selectedType]?['secondaryView'];

    print('selectedAuthType:: ${selectedType}');


    emit(AuthorizationState(
      selectedAuthType: selectedType,
      primaryView: primaryView,
      secondaryView: secondaryView,
    ));
  }




  Map<String, Map<String, Widget>> _getDynamicViews() {
    return {
      'Inherit auth from parent': {
        'primaryView': InheritAuthFromParent(),
        'secondaryView': InheritAuthFromParentAdditional(),
      },
      'No Auth': {
        'primaryView': NoAuth(),
        'secondaryView': NoAuthAdditional(),
      },
      'Basic Auth': {
        'primaryView': BasicAuth(),
        'secondaryView': BasicAuthAdditional(),
      },
      'Bearer Token': {
        'primaryView': BearerToken(),
        'secondaryView': BearerTokenAdditional(),
      },
      'JWT Bearer': {
        'primaryView': JwtBearer(),
        'secondaryView': JWTBearerAdditional(),
      },
      'API Key': {
        'primaryView': ApiKey(),
        'secondaryView': ApiKeyAdditional(),
      },
    };
  }
}*/
