import 'package:postman_flutter/features/home/<USER>/authorization/api_key/api_key_data.dart';
import 'package:postman_flutter/features/home/<USER>/authorization/bloc/auth_subviews_event.dart';
import 'package:postman_flutter/features/home/<USER>/authorization/jwt_bearer/jwt_bearer_data.dart';

import '../basic_auth/basic_auth.dart';
import '../basic_auth/basic_auth_data.dart';
import '../bearer_token/bearer_token_data.dart';

class AuthSubViewsState {
  final String authType;
  final BasicAuthFields basicAuthFields;
  final BearerTokenFields bearerTokenFields;
  final ApiKeyFields apiKeyFields;
  final JwtBearerFields jwtBearerFields;

  const AuthSubViewsState({
    required this.authType,
    required this.basicAuthFields,
    required this.bearerTokenFields,
    required this.apiKeyFields,
    required this.jwtBearerFields
  });

  AuthSubViewsState copyWith({
    String? authType,
    BasicAuthFields? basicAuthFields,
    BearerTokenFields? bearerTokenFields,
    ApiKeyFields? api<PERSON><PERSON><PERSON>ields,
    JwtBearerFields? jwtBearerFields
  }) {
    return AuthSubViewsState(
      authType: authType ?? this.authType,
      basicAuthFields: basicAuthFields ?? this.basicAuthFields,
      bearerTokenFields: bearerTokenFields ?? this.bearerTokenFields,
      apiKeyFields: apiKeyFields ?? this.apiKeyFields,
      jwtBearerFields: jwtBearerFields ?? this.jwtBearerFields,
    );
  }
}
