import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../basic_auth/basic_auth_data.dart';
//old
/*class AuthorizationState extends Equatable {
  final String selectedAuthType;
  final Widget? primaryView;
  final Widget? secondaryView;

  AuthorizationState({
    required this.selectedAuthType,
    required this.primaryView,
    required this.secondaryView,
  });

  @override
  List<Object?> get props => [selectedAuthType, primaryView, secondaryView];
}*/


/*class AuthorizationState extends Equatable {
  final String selectedAuthType;
  final Widget? primaryView;
  final Widget? secondaryView;


  AuthorizationState({
    required this.selectedAuthType,
    required this.primaryView,
    required this.secondaryView,
  });

  AuthorizationState copyWith({
    String? selectedAuthType,
    Widget? primaryView,
    Widget? secondaryView,

  }) {
    return AuthorizationState(
      selectedAuthType: selectedAuthType ?? this.selectedAuthType,
      primaryView: primaryView ?? this.primaryView,
      secondaryView: secondaryView ?? this.secondaryView,
    );
  }

  @override
  List<Object?> get props => [selectedAuthType, primaryView, secondaryView];
}*/



/*class AuthorizationState {
  final String selectedAuthType;
  final Widget? primaryView;
  final Widget? secondaryView;

  AuthorizationState({
    required this.selectedAuthType,
    required this.primaryView,
    required this.secondaryView,
  });

  AuthorizationState copyWith({
    String? selectedAuthType,
    Widget? primaryView,
    Widget? secondaryView,
  }) {
    return AuthorizationState(
      selectedAuthType: selectedAuthType ?? this.selectedAuthType,
      primaryView: primaryView ?? this.primaryView,
      secondaryView: secondaryView ?? this.secondaryView,
    );
  }
}*/
