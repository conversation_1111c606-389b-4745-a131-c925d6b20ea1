import 'package:flutter/material.dart';
import 'package:postman_flutter/helpers/color_config.dart';
import '../../../../helpers/style_config.dart';
import '../../../common_widgets/CommonText.dart';

class NoAuth extends StatefulWidget {
  const NoAuth({super.key});

  @override
  State<NoAuth> createState() => _NoAuthState();
}

class _NoAuthState extends State<NoAuth> {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: CommonText(
        text: 'This request does not use any authorization.',
        textStyle: mRegularTextStyle16(
            textSize: 12, textColor: AppThemeColor.tableTextColor),
      ),
    );
  }
}
