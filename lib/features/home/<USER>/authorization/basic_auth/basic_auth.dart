import 'package:flutter/material.dart';

import '../../../../../globals.dart';
import '../../../../../helpers/color_config.dart';
import '../../../../../helpers/style_config.dart';
import '../../../../common_widgets/CommonText.dart';
import '../../../../common_widgets/CommonTextFormField.dart';
import '../../../../common_widgets/info_icon_button.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/auth_subviews_bloc.dart';
import '../bloc/auth_subviews_event.dart';
import 'basic_auth_data.dart';



class BasicAuth extends StatefulWidget {
  final String username;
  final String password;
  final ValueChanged<BasicAuthFields> onChanged;

  const BasicAuth({
    Key? key,
    required this.username,
    required this.password,
    required this.onChanged,
  }) : super(key: key);

  @override
  State<BasicAuth> createState() => _BasicAuthState();
}

class _BasicAuthState extends State<BasicAuth> {
  late final TextEditingController usernameController;
  late final TextEditingController passwordController;

  @override
  void initState() {
    super.initState();

    // Initialize controllers with the provided values
    usernameController = TextEditingController(text: widget.username);
    passwordController = TextEditingController(text: widget.password);

    // Add listeners to update the external state
    usernameController.addListener(_onFieldChange);
    passwordController.addListener(_onFieldChange);
  }

  void _onFieldChange() {
    widget.onChanged(
      BasicAuthFields(
        username: usernameController.text,
        password: passwordController.text,
      ),
    );
  }

  @override
  void dispose() {
    usernameController.dispose();
    passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 10, right: 20, top: 10, bottom: 0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          const Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(right: 8.0),
                child: InfoIconButton(),
              ),
              Flexible(
                child: Text(
                  "Heads up! These parameters hold sensitive data. To keep this data secure while working in a collaborative environment, we recommend using variables.",
                  maxLines: 100,
                  style: TextStyle(
                    color: AppThemeColor.tabUnselectedTextColor,
                    fontSize: 11,
                    fontFamily: 'inter',
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ],
          ),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.vertical,
              child: Padding(
                padding: const EdgeInsets.only(left: 25.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonTextFormField(
                      labelText: "Username",
                      hintTextString: "Username",
                      isLabelText: true,
                      isLabelTextBold: true,
                      isDense: true,
                      fontSize: 14,
                      hintStyle: TextStyle(color: AppThemeColor.hintTextColor),
                      labelTextSize: Globals.labelTextSizeForDialog,
                      textEditController: usernameController,
                      cornerRadius: 4,
                      errorMessage: "Enter Username",
                      padding: EdgeInsets.only(top: 20),
                    ),
                    CommonTextFormField(
                      labelText: "Password",
                      hintTextString: "Password",
                      isLabelText: true,
                      isLabelTextBold: true,
                      inputType: InputType.Password,
                      isDense: true,
                      fontSize: 14,
                      hintStyle: TextStyle(color: AppThemeColor.hintTextColor),
                      labelTextSize: Globals.labelTextSizeForDialog,
                      textEditController: passwordController,
                      cornerRadius: 4,
                      padding: EdgeInsets.only(top: 20),
                      errorMessage: "Enter Password",
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}


//tested code
/*class BasicAuth extends StatefulWidget {
  const BasicAuth({super.key});

  @override
  State<BasicAuth> createState() => _BasicAuthState();
}

class _BasicAuthState extends State<BasicAuth> {

  final usernameController = TextEditingController();
  final passwordController = TextEditingController();

  @override
  void initState() {
    super.initState();
    usernameController.addListener(_updateAuthData);
    passwordController.addListener(_updateAuthData);
  }

  void _updateAuthData() {
    context.read<AuthSubViewsBloc>().add(
      BasicAuthFieldsChanged(
        bsicAuthFields: BasicAuthFields(
          username: usernameController.text,
          password: passwordController.text,
        ),
      ),
    );
  }


  @override
  void dispose() {
    usernameController.dispose();
    passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
       padding: const EdgeInsets.only(left: 10, right: 20, top: 10, bottom: 0),
       child: Column(
         mainAxisAlignment: MainAxisAlignment.start,
         children: [
           const Row(
             crossAxisAlignment: CrossAxisAlignment.start,
             children: [
               Padding(
                   padding: EdgeInsets.only(right: 8.0),
                   child: InfoIconButton()
                 // Icon(Icons.info_outline, size: 16, color: AppThemeColor.tabUnselectedTextColor,),
               ),
               Flexible(
                 child: Text("Heads up! These parameters hold sensitive data. To keep this data secure while working in a collaborative environment, we recommend using variables.",
                   maxLines: 100,
                   style: TextStyle(color: AppThemeColor.tabUnselectedTextColor, fontSize: 11, fontFamily: 'inter', fontWeight: FontWeight.w400),
                 ),
               ),
             ],
           ),
           Expanded(
             child: SingleChildScrollView(
               scrollDirection: Axis.vertical,
               child: Padding(
                 padding: const EdgeInsets.only(left: 25.0),
                 child: Column(
                   crossAxisAlignment: CrossAxisAlignment.start,
                   children: [

                     CommonTextFormField(
                     labelText: "Username",
                     hintTextString: "Username",
                     isLabelText: true,
                     isLabelTextBold: true,
                     isDense: true,
                     fontSize: 14,
                     hintStyle: TextStyle(
                         color: AppThemeColor.hintTextColor),
                     labelTextSize: Globals.labelTextSizeForDialog,
                     textEditController: usernameController,
                     cornerRadius: 4,
                     errorMessage: "Enter Username",
                     padding: EdgeInsets.only(top: 20),
                     ),

                     CommonTextFormField(
                       labelText: "Password",
                       hintTextString: "Password",
                       isLabelText: true,
                       isLabelTextBold: true,
                       inputType: InputType.Password,
                       isDense: true,
                       fontSize: 14,
                       hintStyle: TextStyle(
                           color: AppThemeColor.hintTextColor),
                       labelTextSize: Globals.labelTextSizeForDialog,
                       textEditController: passwordController,
                       cornerRadius: 4,
                       padding: EdgeInsets.only(top: 20),
                       errorMessage: "Enter Password",
                     ),
                   ],
                 ),
               ),
             ),
           )
         ],
       ),
     );
  }
}*/