import 'package:equatable/equatable.dart';


class BasicAuthFields {
  final String username;
  final String password;

  BasicAuthFields({required this.username, required this.password});

  BasicAuthFields copyWith({
    String? username,
    String? password,
  }) {
    return BasicAuthFields(
      username: username ?? this.username,
      password: password ?? this.password,
    );
  }
}


/*class BasicAuthData{
  final String username;
  final String password;

  BasicAuthData({required this.username, required this.password});

  BasicAuthData copyWith({
    String? username,
    String? password,
  }) {
    return BasicAuthData(
      username: username ?? this.username,
      password: password ?? this.password,
    );
  }
}*/
