import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../globals.dart';
import '../../../../../helpers/color_config.dart';
import '../../../../common_widgets/CommonText.dart';
import '../../../../common_widgets/CommonTextFormField.dart';
import '../../../../common_widgets/common_dropdown.dart';
import '../../../../common_widgets/info_icon_button.dart';
import '../bloc/auth_subviews_bloc.dart';
import '../bloc/auth_subviews_event.dart';
import 'api_key_data.dart';

class ApiKey extends StatefulWidget {
  final String apiKey;
  final String value;
  final String addTo;
  final ValueChanged<ApiKeyFields> onChanged;

  const ApiKey({
    Key? key,
    required this.api<PERSON><PERSON>,
    required this.value,
    required this.addTo,
    required this.onChanged,
  }) : super(key: key);

  @override
  State<ApiKey> createState() => _ApiKeyState();
}

class _ApiKeyState extends State<ApiKey> {
  late final TextEditingController keyController;
  late final TextEditingController valueController;
  late final ValueNotifier<String> addToNotifier;

  @override
  void initState() {
    super.initState();

    // Initialize controllers and notifier with the provided values
    keyController = TextEditingController(text: widget.apiKey);
    valueController = TextEditingController(text: widget.value);
    addToNotifier = ValueNotifier(widget.addTo);

    // Add listeners to update the external state
    keyController.addListener(_onFieldChange);
    valueController.addListener(_onFieldChange);
    addToNotifier.addListener(_onFieldChange);
  }

  void _onFieldChange() {
    widget.onChanged(
      ApiKeyFields(
        key: keyController.text,
        value: valueController.text,
        addToHeaderOrQueryParams: addToNotifier.value,
      ),
    );
  }

  @override
  void dispose() {
    keyController.dispose();
    valueController.dispose();
    addToNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 10, right: 20, top: 10, bottom: 10),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(right: 8.0),
                child: InfoIconButton(),
              ),
              Flexible(
                child: Text(
                  "Heads up! These parameters hold sensitive data. To keep this data secure while working in a collaborative environment, we recommend using variables.",
                  style: TextStyle(
                    color: AppThemeColor.tabUnselectedTextColor,
                    fontSize: 11,
                    fontFamily: 'inter',
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ],
          ),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.vertical,
              child: Padding(
                padding: const EdgeInsets.only(left: 25.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonTextFormField(
                      labelText: "Key",
                      hintTextString: "Key",
                      isLabelText: true,
                      isLabelTextBold: true,
                      isDense: true,
                      fontSize: 14,
                      hintStyle: TextStyle(color: AppThemeColor.hintTextColor),
                      labelTextSize: Globals.labelTextSizeForDialog,
                      textEditController: keyController,
                      cornerRadius: 4,
                      errorMessage: "Enter Key",
                      padding: EdgeInsets.only(top: 20),
                    ),
                    CommonTextFormField(
                      labelText: "Value",
                      hintTextString: "Value",
                      isLabelText: true,
                      isLabelTextBold: true,
                      inputType: InputType.Password,
                      isDense: true,
                      fontSize: 14,
                      hintStyle: TextStyle(color: AppThemeColor.hintTextColor),
                      labelTextSize: Globals.labelTextSizeForDialog,
                      textEditController: valueController,
                      cornerRadius: 4,
                      padding: EdgeInsets.only(top: 20),
                      errorMessage: "Enter Value",
                    ),
                    const SizedBox(height: 20),
                    CommonText(
                      text: 'Add To',
                      textStyle: TextStyle(
                        color: AppThemeColor.white,
                        fontSize: Globals.labelTextSizeForDialog,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ValueListenableBuilder<String>(
                      valueListenable: addToNotifier,
                      builder: (context, value, child) {
                        return CommonDropdown(
                          initialText: value,
                          items: ["Header", "Query Params"],
                          backgroundColor: AppThemeColor.commonBackground,
                          dropdownBackgroundColor: Colors.grey[800]!,
                          textStyle: const TextStyle(color: Colors.white),
                          textPadding: const EdgeInsets.only(
                            right: 10,
                            top: 0,
                            bottom: 0,
                            left: 10,
                          ),
                          iconPadding: const EdgeInsets.only(left: 4),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 10,
                            vertical: 0,
                          ),
                          isBorder: true,
                          height: Globals.dropdownHeight,
                          onChanged: (newValue) {
                            addToNotifier.value = newValue;
                          },
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}


/*class ApiKey extends StatefulWidget {
  const ApiKey({super.key});

  @override
  State<ApiKey> createState() => _ApiKeyState();
}


class _ApiKeyState extends State<ApiKey> {

  final keyController = TextEditingController();
  final valueController = TextEditingController();
  final selectedAddToOption = ValueNotifier<String>("Header"); // Using ValueNotifier

  @override
  void initState() {
    super.initState();
    keyController.addListener(_updateApiKeyData);
    valueController.addListener(_updateApiKeyData);
    selectedAddToOption.addListener(_updateApiKeyData); // Listen for changes to the dropdown
  }

  void _updateApiKeyData() {
    context.read<AuthSubViewsBloc>().add(
      ApiKeyFieldsChanged(
        apiKeyFields: ApiKeyFields(
          key: keyController.text,
          value: valueController.text,
          addToHeaderOrQueryParams: selectedAddToOption.value.isNotEmpty
              ? selectedAddToOption.value
              : 'Header', // Use ValueNotifier's value
        ),
      ),
    );
  }

  @override
  void dispose() {
    keyController.dispose();
    valueController.dispose();
    selectedAddToOption.dispose(); // Dispose ValueNotifier
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 10, right: 20, top: 10, bottom: 10),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(right: 8.0),
                child: InfoIconButton(),
              ),
              Flexible(
                child: Text(
                  "Heads up! These parameters hold sensitive data. To keep this data secure while working in a collaborative environment, we recommend using variables.",
                  style: TextStyle(
                      color: AppThemeColor.tabUnselectedTextColor,
                      fontSize: 11,
                      fontFamily: 'inter',
                      fontWeight: FontWeight.w400),
                ),
              ),
            ],
          ),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.vertical,
              child: Padding(
                padding: const EdgeInsets.only(left: 25.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonTextFormField(
                      labelText: "Key",
                      hintTextString: "Key",
                      isLabelText: true,
                      isLabelTextBold: true,
                      isDense: true,
                      fontSize: 14,
                      hintStyle:
                      TextStyle(color: AppThemeColor.hintTextColor),
                      labelTextSize: Globals.labelTextSizeForDialog,
                      textEditController: keyController,
                      cornerRadius: 4,
                      errorMessage: "Enter Key",
                      padding: EdgeInsets.only(top: 20),
                    ),
                    CommonTextFormField(
                      labelText: "Value",
                      hintTextString: "Value",
                      isLabelText: true,
                      isLabelTextBold: true,
                      inputType: InputType.Password,
                      isDense: true,
                      fontSize: 14,
                      hintStyle:
                      TextStyle(color: AppThemeColor.hintTextColor),
                      labelTextSize: Globals.labelTextSizeForDialog,
                      textEditController: valueController,
                      cornerRadius: 4,
                      padding: EdgeInsets.only(top: 20),
                      errorMessage: "Value Password",
                    ),
                    const SizedBox(height: 20),
                    CommonText(
                      text: 'Add To',
                      textStyle: TextStyle(
                          color: AppThemeColor.white,
                          fontSize: Globals.labelTextSizeForDialog),
                    ),
                    const SizedBox(height: 8),
                    ValueListenableBuilder<String>(
                      valueListenable: selectedAddToOption,
                      builder: (context, value, child) {
                        return CommonDropdown(
                          initialText: selectedAddToOption.value,
                          items: ["Header", "Query Params"],
                          backgroundColor: AppThemeColor.commonBackground,
                          dropdownBackgroundColor: Colors.grey[800]!,
                          textStyle: TextStyle(color: Colors.white),
                          textPadding: EdgeInsets.only(
                              right: 10, top: 0, bottom: 0, left: 10),
                          iconPadding: EdgeInsets.only(left: 4),
                          padding: EdgeInsets.symmetric(
                              horizontal: 10, vertical: 0),
                          isBorder: true,
                          height: Globals.dropdownHeight,
                          onChanged: (newValue) {
                            selectedAddToOption.value = newValue; // Update ValueNotifier
                            print("Selected: $newValue");
                          },
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}*/



