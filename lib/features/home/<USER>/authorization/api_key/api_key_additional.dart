import 'package:flutter/material.dart';

import '../../../../../helpers/color_config.dart';
import '../../../../../helpers/style_config.dart';
import '../../../../common_widgets/CommonText.dart';

class ApiKeyAdditional extends StatefulWidget {
  const ApiKeyAdditional({super.key});

  @override
  State<ApiKeyAdditional> createState() => _ApiKeyAdditionalState();
}

class _ApiKeyAdditionalState extends State<ApiKeyAdditional> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(top: 10),
      child: CommonText(
        text: 'The authorization header will be automatically generated when you send the request. Learn more about API Key authorization. ',
        maxLine: 1000,
        textStyle: mRegularTextStyle16(
            textSize: 12, textColor: AppThemeColor.tableTextColor),
      ),
    );
  }
}
