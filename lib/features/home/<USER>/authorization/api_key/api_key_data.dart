

class ApiKeyFields {
  final String key;
  final String value;
  final String addToHeaderOrQueryParams;

  Api<PERSON>eyFields({required this.key, required this.value, required this.addToHeaderOrQueryParams});

  ApiKeyFields copyWith({
    String? key,
    String? value,
    String? addToHeaderOrQueryParams,
  }) {
    return ApiKeyFields(
      key: key ?? this.key,
      value: value ?? this.value,
      addToHeaderOrQueryParams: addToHeaderOrQueryParams ?? this.addToHeaderOrQueryParams,
    );
  }
}