import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/authorization/bloc/auth_subviews_event.dart';
import '../../../../helpers/color_config.dart';
import '../../../../helpers/style_config.dart';
import '../../../common_widgets/CommonText.dart';
import '../../../common_widgets/common_dropdown_with_views.dart';
import 'api_key/api_key.dart';
import 'api_key/api_key_additional.dart';
import 'basic_auth/basic_auth.dart';
import 'basic_auth/basic_auth_additional.dart';
import 'bearer_token/bearer_token.dart';
import 'bearer_token/bearer_token_additional.dart';
import 'bearer_token/bearer_token_data.dart';
import 'bloc/auth_subviews_bloc.dart';
import 'bloc/auth_subviews_state.dart';
import 'bloc/authorization_bloc.dart';
import 'bloc/authorization_event.dart';
import 'bloc/authorization_state.dart';
import 'inherit_auth_from_parent.dart';
import 'inherit_auth_from_parent_additional.dart';
import 'jwt_bearer/jwt_bearer.dart';
import 'jwt_bearer/jwt_bearer_additional.dart';
import 'no_auth.dart';
import 'no_auth_additional.dart';


class Authorization extends StatefulWidget {
  const Authorization({super.key});

  @override
  State<Authorization> createState() => _AuthorizationState();
}

class _AuthorizationState extends State<Authorization> {


  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthSubViewsBloc, AuthSubViewsState>(
  builder: (context, state) {

    final bloc = context.read<AuthSubViewsBloc>();

    Widget? primaryView;
    Widget? secondaryView;

    switch (state.authType) {
      case 'Basic Auth':
        primaryView = BasicAuth(
          username: state.basicAuthFields.username,
          password: state.basicAuthFields.password,
          onChanged: (fields) => bloc.add(BasicAuthFieldsChanged(bsicAuthFields: fields)),
        );
        secondaryView = const BasicAuthAdditional();
        break;

      case 'Bearer Token':
        primaryView = BearerToken(
          token: state.bearerTokenFields.token,
          onChanged: (token) =>
              bloc.add(BearerTokenFieldsChanged(bearerTokenFields: BearerTokenFields(token: token))),
        );
        secondaryView = const BearerTokenAdditional();
        break;

      case 'API Key':
        primaryView = ApiKey(
          apiKey: state.apiKeyFields.key,
          value: state.apiKeyFields.value,
          addTo: state.apiKeyFields.addToHeaderOrQueryParams,
          onChanged: (fields) => bloc.add(ApiKeyFieldsChanged(apiKeyFields: fields)),
        );

        secondaryView = const ApiKeyAdditional();
        break;

      /*case 'JWT Bearer':
        primaryView = JwtBearer(
          jwtBearerFields: state.jwtBearerFields,
          onChanged: (fields) => bloc.add(JwtBearerFieldsChanged(jwtBearerFields: fields)),
        );

        secondaryView = const JWTBearerAdditional();
        break;*/

      case 'No Auth':
      default:
        primaryView = const NoAuth();
        secondaryView = const NoAuthAdditional();
    }


    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Container(
            margin: EdgeInsets.zero,
            padding: const EdgeInsets.only(bottom: 0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: const EdgeInsets.only(bottom: 5),
                      child: CommonText(
                        text: 'Auth Type',
                        textStyle: mMediumTextStyle16(
                          textSize: 14,
                          textColor: AppThemeColor.infoIconColor,
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 40,
                      width: 277,
                      child: CommonDropdownWithViews(
                        initialText: state.authType,
                        items: [
                          //"Inherit auth from parent",
                          "No Auth",
                          "Basic Auth",
                          "Bearer Token",
                          //"JWT Bearer",
                          "API Key"
                        ],
                        backgroundColor: AppThemeColor.lightBackground,
                        dropdownBackgroundColor: Colors.grey[800]!,
                        textStyle: const TextStyle(color: Colors.white),
                        textPadding: const EdgeInsets.only(right: 4, left: 10, top: 5, bottom: 5),
                        iconPadding: const EdgeInsets.only(left: 15, right: 5),
                        padding: const EdgeInsets.only(left: 15, right: 4),
                        onChanged: (authType) {
                          bloc.add(AuthTypeChanged(authType: authType));
                        },
                      ),
                    ),
                    Expanded(
                      child: SingleChildScrollView(
                        scrollDirection: Axis.vertical,
                        child: Container(
                          width: 277,
                          child: secondaryView
                        ),
                      ),
                    ),
                  ],
                ),
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(0),
                      border: Border(
                        top: BorderSide(color: AppThemeColor.dividerBackgroundColor, width: 1),
                        left: BorderSide(color: AppThemeColor.dividerBackgroundColor, width: 1),
                      ),
                    ),
                    margin: const EdgeInsets.only(left: 20),
                    child: primaryView
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  },
);
  }
}


// tested with set state
/*class Authorization extends StatefulWidget {
  const Authorization({super.key});

  @override
  State<Authorization> createState() => _AuthorizationState();
}

class _AuthorizationState extends State<Authorization> {
  final PageStorageBucket _bucket = PageStorageBucket();
  Widget? _currentPrimaryView;
  Widget? _currentSecondaryView;

  final Map<String, Map<String, Widget>> dynamicViews = {
    'Inherit auth from parent': {
      'primaryView': InheritAuthFromParent(),
      'secondaryView': InheritAuthFromParentAdditional(),
    },
    'No Auth': {
      'primaryView': NoAuth(),
      'secondaryView': NoAuthAdditional(),
    },
    'Basic Auth': {
      'primaryView': BasicAuth(),
      'secondaryView': BasicAuthAdditional(),
    },
    'Bearer Token': {
      'primaryView': BearerToken(),
      'secondaryView': BearerTokenAdditional(),
    },
    'JWT Bearer': {
      'primaryView': JwtBearer(),
      'secondaryView': JWTBearerAdditional(),
    },
    'API Key': {
      'primaryView': ApiKey(),
      'secondaryView': ApiKeyAdditional(),
    },
  };

  void _onAuthTypeChanged(String? value) {
    if (value != null) {
      // Dispatch the selected auth type to the Bloc
      context.read<AuthSubViewsBloc>().add(AuthTypeChanged(authType: value));

      print('authT:: ${value}');

      // Update local state for views
      setState((){
        if (dynamicViews.containsKey(value)) {
          _currentPrimaryView = dynamicViews[value]!['primaryView'];
          _currentSecondaryView = dynamicViews[value]!['secondaryView'];
        } else {
          _currentPrimaryView = null;
          _currentSecondaryView = null;
        }}
      );
    }
  }

  @override
  void initState() {
    super.initState();
    // Initialize the default dropdown value
    _onAuthTypeChanged("Inherit auth from parent");
  }

  @override
  Widget build(BuildContext context) {
    return PageStorage(
      bucket: _bucket,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Container(
              margin: EdgeInsets.zero,
              padding: const EdgeInsets.only(bottom: 0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding: const EdgeInsets.only(bottom: 5),
                        child: CommonText(
                          text: 'Auth Type',
                          textStyle: mMediumTextStyle16(
                            textSize: 14,
                            textColor: AppThemeColor.infoIconColor,
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 40,
                        width: 277,
                        child: CommonDropdownWithViews(
                          initialText: "Inherit auth from parent",
                          items: [
                            "Inherit auth from parent",
                            "No Auth",
                            "Basic Auth",
                            "Bearer Token",
                            "JWT Bearer",
                            "API Key"
                          ],
                          backgroundColor: AppThemeColor.lightBackground,
                          dropdownBackgroundColor: Colors.grey[800]!,
                          textStyle: const TextStyle(color: Colors.white),
                          textPadding: const EdgeInsets.only(right: 4, left: 10, top: 5, bottom: 5),
                          iconPadding: const EdgeInsets.only(left: 15, right: 5),
                          padding: const EdgeInsets.only(left: 15, right: 4),
                          onChanged: _onAuthTypeChanged,
                        ),
                      ),
                      Expanded(
                        child: SingleChildScrollView(
                          scrollDirection: Axis.vertical,
                          child: Container(
                            width: 277,
                            child: _currentSecondaryView ??
                                Center(
                                  child: Text(
                                    "",
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.white70,
                                    ),
                                  ),
                                ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(0),
                        border: Border(
                          top: BorderSide(color: AppThemeColor.dividerBackgroundColor, width: 1),
                          left: BorderSide(color: AppThemeColor.dividerBackgroundColor, width: 1),
                        ),
                      ),
                      margin: const EdgeInsets.only(left: 20),
                      child: _currentPrimaryView ??
                          Center(
                            child: Text(
                              "Select an Auth Type",
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.white70,
                              ),
                            ),
                          ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}*/

