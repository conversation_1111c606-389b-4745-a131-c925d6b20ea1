import 'package:flutter/material.dart';

import '../../../../helpers/color_config.dart';
import '../../../../helpers/style_config.dart';
import '../../../common_widgets/CommonText.dart';

class InheritAuthFromParent extends StatefulWidget {
  const InheritAuthFromParent({super.key});

  @override
  State<InheritAuthFromParent> createState() => _InheritAuthFromParentState();
}

class _InheritAuthFromParentState extends State<InheritAuthFromParent> {
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Center(
          child: Flexible(
            child: CommonText(
              text: 'This request is using No Auth from collection Test API.',
              textStyle: mRegularTextStyle16(
                  textSize: 12, textColor: AppThemeColor.tableTextColor),
            ),
          ),
        ),
      ],
    );
  }
}
