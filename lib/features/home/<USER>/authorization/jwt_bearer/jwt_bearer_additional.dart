import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../helpers/color_config.dart';
import '../../../../../helpers/style_config.dart';
import '../../../../common_widgets/CommonText.dart';
import '../../../../common_widgets/common_dropdown.dart';
import '../../../../common_widgets/common_dropdown_with_views.dart';
import '../bloc/auth_subviews_bloc.dart';
import '../bloc/auth_subviews_event.dart';
import '../bloc/auth_subviews_state.dart';




class JWTBearerAdditional extends StatefulWidget {
  const JWTBearerAdditional({super.key});

  @override
  State<JWTBearerAdditional> createState() => _JWTBearerAdditionalState();
}

class _JWTBearerAdditionalState extends State<JWTBearerAdditional> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 10),
        CommonText(
          text: 'The authorization header will be automatically generated when you send the request. Learn more about authorization.',
          maxLine: 1000,
          textStyle: mRegularTextStyle16(
              textSize: 12, textColor: AppThemeColor.tableTextColor),
        ),
        SizedBox(height: 30),
        CommonText(
          text: 'Add JWT token to',
          textStyle: mMediumTextStyle16(
              textSize: 14, textColor: AppThemeColor.infoIconColor),
        ),
        SizedBox(height: 5),
        SizedBox(
          height: 40,
          width: 277,
          child: BlocBuilder<AuthSubViewsBloc, AuthSubViewsState>(
            builder: (context, state) {
              return CommonDropdown(
                initialText: state.jwtBearerFields.jwtTokenToHeaderOrQueryParams,
                items: ["Request Header", "Query Params"],
                backgroundColor: AppThemeColor.lightBackground,
                dropdownBackgroundColor: Colors.grey[800]!,
                textStyle: TextStyle(color: Colors.white),
                textPadding: const EdgeInsets.only(right: 4, left: 10),
                iconPadding: const EdgeInsets.only(left: 15, right: 5),
                padding: const EdgeInsets.only(left: 15, right: 4),
                onChanged: (value) {
                  context.read<AuthSubViewsBloc>().add(
                    JwtBearerFieldsChanged(
                      jwtBearerFields: state.jwtBearerFields.copyWith(
                        jwtTokenToHeaderOrQueryParams: value,
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ),
      ],
    );
  }
}




/*class JWTBearerAdditional extends StatefulWidget {
  const JWTBearerAdditional({super.key});

  @override
  State<JWTBearerAdditional> createState() => _JWTBearerAdditionalState();
}

class _JWTBearerAdditionalState extends State<JWTBearerAdditional> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [

        SizedBox(height: 10,),

        CommonText(
          text: 'The authorization header will be automatically generated when you send the request. Learn more about authorization.',
          maxLine: 1000,
          textStyle: mRegularTextStyle16(
              textSize: 12, textColor: AppThemeColor.tableTextColor),
        ),

        SizedBox(height: 30,),

        CommonText(
          text: 'Add JWT token to',
          //maxLine: 1000,
          textStyle: mMediumTextStyle16(
              textSize: 14, textColor: AppThemeColor.infoIconColor),
        ),

        SizedBox(height: 5,),

        SizedBox(
          height: 40,
          width: 277,
          child: CommonDropdown(
            initialText: "Request Header",
            //dropdownIcon: Icon(Icons.arrow_drop_down, color: Colors.white),
            items: ["Request Header", "Query Params"],
            backgroundColor: AppThemeColor.lightBackground,
            dropdownBackgroundColor: Colors.grey[800]!,
            textStyle: TextStyle(color: Colors.white),
            textPadding: const EdgeInsets.only(right: 4, left: 10),
            iconPadding: const EdgeInsets.only(left: 15, right: 5),
            padding: const EdgeInsets.only(left: 15, right: 4),
            onChanged: (value) {
              print("Selected: $value");
            },
          ),
        ),

      ],
    );
  }
}*/
