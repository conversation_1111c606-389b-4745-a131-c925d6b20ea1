class JwtBearerFields {
  final String algorithm;
  final String secretToken;
  final String payload;
  final String requestHeaderprefix;
  final String jwtHeader;
  String? jwtTokenToHeaderOrQueryParams = 'Request Header';

  JwtBearerFields({required this.algorithm,
    required this.secretToken,
    required this.payload,
    required this.requestHeaderprefix,
    required this.jwtHeader,
    required this.jwtTokenToHeaderOrQueryParams
  });

  JwtBearerFields copyWith({
    String? algorithm,
    String? secretToken,
    String? payload,
    String? requestHeaderprefix,
    String? jwtHeader,
    String? jwtTokenToHeaderOrQueryParams,
  }) {
    return JwtBearerFields(
      algorithm: algorithm ?? this.algorithm,
      secretToken: secretToken ?? this.secretToken,
      payload: payload ?? this.payload,
      requestHeaderprefix: requestHeaderprefix ?? this.requestHeaderprefix,
      jwtHeader: jwtHeader ?? this.jwtHeader,
      jwtTokenToHeaderOrQueryParams: jwtTokenToHeaderOrQueryParams ?? this.jwtTokenToHeaderOrQueryParams,
    );
  }
}