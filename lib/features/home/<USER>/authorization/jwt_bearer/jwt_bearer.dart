import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_code_editor/flutter_code_editor.dart';
import 'package:flutter_highlight/themes/a11y-dark.dart';

import '../../../../../globals.dart';
import '../../../../../helpers/color_config.dart';
import '../../../../../helpers/style_config.dart';
import '../../../../common_widgets/CommonText.dart';
import '../../../../common_widgets/CommonTextFormField.dart';
import '../../../../common_widgets/common_dropdown.dart';
import '../../../../common_widgets/custom_checkbox.dart';
import '../../../../common_widgets/info_icon_button.dart';
import 'package:highlight/languages/json.dart' show json;
import 'dart:convert' as dc;

import '../bloc/auth_subviews_bloc.dart';
import '../bloc/auth_subviews_event.dart';
import 'jwt_bearer_data.dart';

class JwtBearer extends StatefulWidget {
  final JwtBearerFields jwtBearerFields;
  final ValueChanged<JwtBearerFields> onChanged;

  const JwtBearer({
    Key? key,
    required this.jwtBearerFields,
    required this.onChanged,
  }) : super(key: key);

  @override
  State<JwtBearer> createState() => _JwtBearerState();
}

class _JwtBearerState extends State<JwtBearer> {
  late final TextEditingController algorithmController;
  late final TextEditingController secretController;
  late final TextEditingController requestHeaderPrefixController;
  late final CodeController payloadController;
  late final CodeController jwtHeaderController;
  late final ValueNotifier<bool> isBase64;
  String jwtTokenLocation = 'Request Header';

  @override
  void initState() {
    super.initState();

    // Initialize controllers with the provided values
    algorithmController = TextEditingController(text: widget.jwtBearerFields.algorithm);
    secretController = TextEditingController(
      text: widget.jwtBearerFields.secretToken.startsWith('Base64:')
          ? widget.jwtBearerFields.secretToken.substring(7)
          : widget.jwtBearerFields.secretToken,
    );
    requestHeaderPrefixController = TextEditingController(text: widget.jwtBearerFields.requestHeaderprefix);
    payloadController = CodeController(
      text: widget.jwtBearerFields.payload,
      language: json,
    );
    jwtHeaderController = CodeController(
      text: widget.jwtBearerFields.jwtHeader,
      language: json,
    );
    isBase64 = ValueNotifier(widget.jwtBearerFields.secretToken.startsWith('Base64:'));
    jwtTokenLocation = widget.jwtBearerFields.jwtTokenToHeaderOrQueryParams ?? 'Request Header';

    // Add listeners to update the state
    _addListeners();
  }

  void _addListeners() {
    algorithmController.addListener(_updateJwtBearerData);
    secretController.addListener(_updateJwtBearerData);
    requestHeaderPrefixController.addListener(_updateJwtBearerData);
    payloadController.addListener(_updateJwtBearerData);
    jwtHeaderController.addListener(_updateJwtBearerData);
    isBase64.addListener(_updateJwtBearerData);
  }

  void _updateJwtBearerData() {
    widget.onChanged(
      JwtBearerFields(
        algorithm: algorithmController.text,
        secretToken: isBase64.value
            ? 'Base64:${secretController.text}'
            : secretController.text,
        payload: payloadController.text,
        requestHeaderprefix: requestHeaderPrefixController.text,
        jwtHeader: jwtHeaderController.text,
        jwtTokenToHeaderOrQueryParams: jwtTokenLocation,
      ),
    );
  }

  @override
  void dispose() {
    algorithmController.dispose();
    secretController.dispose();
    requestHeaderPrefixController.dispose();
    payloadController.dispose();
    jwtHeaderController.dispose();
    isBase64.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.vertical,
      child: Padding(
        padding: const EdgeInsets.only(left: 10, right: 20, top: 10, bottom: 10),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                    padding: EdgeInsets.only(right: 8.0),
                    child: InfoIconButton()
                  //Icon(Icons.info_outline, size: 16, color: AppThemeColor.tabUnselectedTextColor,),
                ),
                Flexible(
                  child: Text("Heads up! These parameters hold sensitive data. To keep this data secure while working in a collaborative environment, we recommend using variables.",
                    style: TextStyle(color: AppThemeColor.tabUnselectedTextColor, fontSize: 11, fontFamily: 'inter', fontWeight: FontWeight.w400),
                  ),
                ),
              ],
            ),

            SizedBox(height: 20,),

            Padding(
              padding: const EdgeInsets.only(left: 25.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CommonText(
                        text: 'Algorithm',
                        textStyle: mMediumTextStyle16(
                            textSize: 14, textColor: AppThemeColor.white),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(left: 8.0),
                        child: InfoIconButton(
                          hoverText: "Algorithm used to generate the JWT signature",
                          onHoverInfoText: true,
                        ),
                      ),

                    ],
                  ),
                  const SizedBox(height: 8),

                  CommonTextFormField(
                    labelText: "Algorithm",
                    hintTextString: "HS256",
                    isLabelText: false,
                    isLabelTextBold: false,
                    isDense: true,
                    fontSize: 14,
                    hintStyle: TextStyle(
                        color: AppThemeColor.hintTextColor),
                    labelTextSize: Globals.labelTextSizeForDialog,
                    textEditController: algorithmController,
                    cornerRadius: 4,
                    errorMessage: "",
                    padding: EdgeInsets.only(top: 0),
                  ),

                  SizedBox(height: 20,),

                  Row(
                    children: [
                      CommonText(
                        text: 'Secret',
                        textStyle: mMediumTextStyle16(
                            textSize: 14, textColor: AppThemeColor.white),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(left: 8.0),
                        child: InfoIconButton(
                          hoverText: "Secret used to generate the JWT signature",
                          onHoverInfoText: true,
                        ),
                      ),

                    ],
                  ),
                  const SizedBox(height: 8),

                  CommonTextFormField(
                    labelText: "Secret",
                    hintTextString: "Token",
                    isLabelText: false,
                    isLabelTextBold: true,
                    inputType: InputType.Password,
                    isDense: true,
                    fontSize: 14,
                    hintStyle: TextStyle(
                        color: AppThemeColor.hintTextColor),
                    labelTextSize: Globals.labelTextSizeForDialog,
                    textEditController: secretController,
                    cornerRadius: 4,
                    padding: EdgeInsets.only(top: 0),
                    errorMessage: "Value Password",
                  ),

                  const SizedBox(height: 8),


                  ValueListenableBuilder<bool>(
                    valueListenable: isBase64,
                    builder: (context, value, _) {
                      return CustomCheckbox(
                        value: value,
                        label: "Secret Base64",
                        onChanged: (newValue) {
                          isBase64.value = newValue;
                        },
                      );
                    },
                  ),


                  SizedBox(height: 20,),

                  Row(
                    children: [
                      CommonText(
                        text: 'Payload',
                        textStyle: mMediumTextStyle16(
                            textSize: 14, textColor: AppThemeColor.white),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(left: 8.0),
                        child: InfoIconButton(
                          hoverText: "A JSON object that contains the claims conveyed by the JWT",
                          onHoverInfoText: true,
                        ),
                      ),

                    ],
                  ),
                  const SizedBox(height: 8),

                  Container(
                    // constraints: const BoxConstraints(minHeight: 200, maxHeight: 400),
                    height: 100,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: AppThemeColor.commonBorderColor,
                        width: 1.0,
                      ),
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                    child: CodeTheme(
                      data: CodeThemeData(styles: a11yDarkTheme),
                      child: CodeField(
                        background: AppThemeColor.commonBackground,
                        controller: payloadController,
                        gutterStyle: GutterStyle.none,
                        expands: true,
                        wrap: true,
                        textStyle: mRegularTextStyle16(textSize: 12),
                      ),
                    ),
                  ),

                  SizedBox(height: 20,),

                  Row(
                    children: [
                      CommonText(
                        text: 'Request header prefix',
                        textStyle: mMediumTextStyle16(
                            textSize: 14, textColor: AppThemeColor.white),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(left: 8.0),
                        child: InfoIconButton(
                          hoverText: "The prefix added to the authorization header in the request",
                          onHoverInfoText: true,
                        ),
                      ),

                    ],
                  ),
                  const SizedBox(height: 8),

                  CommonTextFormField(
                    labelText: "Request header prefix",
                    hintTextString: "Bearer",
                    isLabelText: false,
                    isLabelTextBold: false,
                    isDense: true,
                    fontSize: 14,
                    hintStyle: TextStyle(
                        color: AppThemeColor.hintTextColor),
                    labelTextSize: Globals.labelTextSizeForDialog,
                    textEditController: requestHeaderPrefixController,
                    cornerRadius: 4,
                    errorMessage: "",
                    padding: EdgeInsets.only(top: 0),
                  ),

                  SizedBox(height: 20,),


                  Row(
                    children: [
                      CommonText(
                        text: 'JWT headers',
                        textStyle: mMediumTextStyle16(
                            textSize: 14, textColor: AppThemeColor.white),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(left: 8.0),
                        child: InfoIconButton(
                          hoverText: "These values will be appended to the JWT headers",
                          onHoverInfoText: true,
                        ),
                      ),

                    ],
                  ),
                  const SizedBox(height: 8),

                  Container(
                    height: 100,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: AppThemeColor.commonBorderColor,
                        width: 1.0,
                      ),
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                    child: CodeTheme(
                      data: CodeThemeData(styles: a11yDarkTheme),
                      child: CodeField(
                        background: AppThemeColor.commonBackground,
                        controller: jwtHeaderController,
                        gutterStyle: GutterStyle.none,
                        expands: true,
                        wrap: true,
                        textStyle: mRegularTextStyle16(textSize: 12),
                      ),
                    ),
                  ),

                  SizedBox(height: 8,),

                  const Text("Headers specific to the algorithm are added automatically.",
                    style: TextStyle(color: AppThemeColor.tabUnselectedTextColor, fontSize: 11, fontFamily: 'inter', fontWeight: FontWeight.w400),
                  ),
                ],),
            )
          ],
        ),
      ),
    );
  }
}





/*class JwtBearer extends StatefulWidget {
  const JwtBearer({super.key});

  @override
  State<JwtBearer> createState() => _JwtBearerState();
}

class _JwtBearerState extends State<JwtBearer> {
  final algorithmController = TextEditingController();
  final secretController = TextEditingController();
  final requestHeaderPrefixController = TextEditingController();
  late final CodeController payloadController;
  late final CodeController jwtHeaderController;
  final isBase64 = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();

    payloadController = CodeController(
      text: '{}', // Default JSON structure
      language: json,
    );

    jwtHeaderController = CodeController(
      text: '{}', // Default JSON structure
      language: json,
    );

    // Listeners for input changes
    algorithmController.addListener(_updateJwtBearerData);
    secretController.addListener(_updateJwtBearerData);
    requestHeaderPrefixController.addListener(_updateJwtBearerData);
    payloadController.addListener(_updateJwtBearerData);
    jwtHeaderController.addListener(_updateJwtBearerData);
    isBase64.addListener(_updateJwtBearerData);
  }

  void _updateJwtBearerData() {
    context.read<AuthSubViewsBloc>().add(
      JwtBearerFieldsChanged(
        jwtBearerFields: JwtBearerFields(
          algorithm: algorithmController.text,
          secretToken: isBase64.value
              ? 'Base64:${secretController.text}'
              : secretController.text,
          payload: payloadController.text,
          requestHeaderprefix: requestHeaderPrefixController.text,
          jwtHeader: jwtHeaderController.text,
          jwtTokenToHeaderOrQueryParams: '',
        ),
      ),
    );
  }


  @override
  void dispose() {
    algorithmController.dispose();
    secretController.dispose();
    requestHeaderPrefixController.dispose();
    payloadController.dispose();
    jwtHeaderController.dispose();
    isBase64.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.vertical,
      child: Padding(
        padding: const EdgeInsets.only(left: 10, right: 20, top: 10, bottom: 10),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                    padding: EdgeInsets.only(right: 8.0),
                    child: InfoIconButton()
                  //Icon(Icons.info_outline, size: 16, color: AppThemeColor.tabUnselectedTextColor,),
                ),
                Flexible(
                  child: Text("Heads up! These parameters hold sensitive data. To keep this data secure while working in a collaborative environment, we recommend using variables.",
                    style: TextStyle(color: AppThemeColor.tabUnselectedTextColor, fontSize: 11, fontFamily: 'inter', fontWeight: FontWeight.w400),
                  ),
                ),
              ],
            ),

            SizedBox(height: 20,),

            Padding(
              padding: const EdgeInsets.only(left: 25.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CommonText(
                        text: 'Algorithm',
                        textStyle: mMediumTextStyle16(
                            textSize: 14, textColor: AppThemeColor.white),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(left: 8.0),
                        child: InfoIconButton(
                          hoverText: "Algorithm used to generate the JWT signature",
                          onHoverInfoText: true,
                        ),
                      ),

                    ],
                  ),
                  const SizedBox(height: 8),

                  CommonTextFormField(
                    labelText: "Algorithm",
                    hintTextString: "HS256",
                    isLabelText: false,
                    isLabelTextBold: false,
                    isDense: true,
                    fontSize: 14,
                    hintStyle: TextStyle(
                        color: AppThemeColor.hintTextColor),
                    labelTextSize: Globals.labelTextSizeForDialog,
                    textEditController: algorithmController,
                    cornerRadius: 4,
                    errorMessage: "",
                    padding: EdgeInsets.only(top: 0),
                  ),

                  SizedBox(height: 20,),

                  Row(
                    children: [
                      CommonText(
                        text: 'Secret',
                        textStyle: mMediumTextStyle16(
                            textSize: 14, textColor: AppThemeColor.white),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(left: 8.0),
                        child: InfoIconButton(
                          hoverText: "Secret used to generate the JWT signature",
                          onHoverInfoText: true,
                        ),
                      ),

                    ],
                  ),
                  const SizedBox(height: 8),

                  CommonTextFormField(
                    labelText: "Secret",
                    hintTextString: "Token",
                    isLabelText: false,
                    isLabelTextBold: true,
                    inputType: InputType.Password,
                    isDense: true,
                    fontSize: 14,
                    hintStyle: TextStyle(
                        color: AppThemeColor.hintTextColor),
                    labelTextSize: Globals.labelTextSizeForDialog,
                    textEditController: secretController,
                    cornerRadius: 4,
                    padding: EdgeInsets.only(top: 0),
                    errorMessage: "Value Password",
                  ),

                  const SizedBox(height: 8),


                  ValueListenableBuilder<bool>(
                    valueListenable: isBase64,
                    builder: (context, value, _) {
                      return CustomCheckbox(
                        value: value,
                        label: "Secret Base64",
                        onChanged: (newValue) {
                          isBase64.value = newValue;
                        },
                      );
                    },
                  ),


                  SizedBox(height: 20,),

                  Row(
                    children: [
                      CommonText(
                        text: 'Payload',
                        textStyle: mMediumTextStyle16(
                            textSize: 14, textColor: AppThemeColor.white),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(left: 8.0),
                        child: InfoIconButton(
                          hoverText: "A JSON object that contains the claims conveyed by the JWT",
                          onHoverInfoText: true,
                        ),
                      ),

                    ],
                  ),
                  const SizedBox(height: 8),

                  Container(
                    // constraints: const BoxConstraints(minHeight: 200, maxHeight: 400),
                    height: 100,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: AppThemeColor.commonBorderColor,
                        width: 1.0,
                      ),
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                    child: CodeTheme(
                      data: CodeThemeData(styles: a11yDarkTheme),
                      child: CodeField(
                        background: AppThemeColor.commonBackground,
                        controller: payloadController,
                        gutterStyle: GutterStyle.none,
                        expands: true,
                        wrap: true,
                        textStyle: mRegularTextStyle16(textSize: 12),
                      ),
                    ),
                  ),

                  SizedBox(height: 20,),

                  Row(
                    children: [
                      CommonText(
                        text: 'Request header prefix',
                        textStyle: mMediumTextStyle16(
                            textSize: 14, textColor: AppThemeColor.white),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(left: 8.0),
                        child: InfoIconButton(
                          hoverText: "The prefix added to the authorization header in the request",
                          onHoverInfoText: true,
                        ),
                      ),

                    ],
                  ),
                  const SizedBox(height: 8),

                  CommonTextFormField(
                    labelText: "Request header prefix",
                    hintTextString: "Bearer",
                    isLabelText: false,
                    isLabelTextBold: false,
                    isDense: true,
                    fontSize: 14,
                    hintStyle: TextStyle(
                        color: AppThemeColor.hintTextColor),
                    labelTextSize: Globals.labelTextSizeForDialog,
                    textEditController: requestHeaderPrefixController,
                    cornerRadius: 4,
                    errorMessage: "",
                    padding: EdgeInsets.only(top: 0),
                  ),

                  SizedBox(height: 20,),


                  Row(
                    children: [
                      CommonText(
                        text: 'JWT headers',
                        textStyle: mMediumTextStyle16(
                            textSize: 14, textColor: AppThemeColor.white),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(left: 8.0),
                        child: InfoIconButton(
                          hoverText: "These values will be appended to the JWT headers",
                          onHoverInfoText: true,
                        ),
                      ),

                    ],
                  ),
                  const SizedBox(height: 8),

                  Container(
                    height: 100,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: AppThemeColor.commonBorderColor,
                        width: 1.0,
                      ),
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                    child: CodeTheme(
                      data: CodeThemeData(styles: a11yDarkTheme),
                      child: CodeField(
                        background: AppThemeColor.commonBackground,
                        controller: jwtHeaderController,
                        gutterStyle: GutterStyle.none,
                        expands: true,
                        wrap: true,
                        textStyle: mRegularTextStyle16(textSize: 12),
                      ),
                    ),
                  ),

                  SizedBox(height: 8,),

                  const Text("Headers specific to the algorithm are added automatically.",
                    style: TextStyle(color: AppThemeColor.tabUnselectedTextColor, fontSize: 11, fontFamily: 'inter', fontWeight: FontWeight.w400),
                  ),
                ],),
            )
          ],
        ),
      ),
    );
  }
}*/





