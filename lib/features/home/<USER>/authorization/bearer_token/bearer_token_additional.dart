import 'package:flutter/material.dart';

import '../../../../../helpers/color_config.dart';
import '../../../../../helpers/style_config.dart';
import '../../../../common_widgets/CommonText.dart';

class BearerTokenAdditional extends StatefulWidget {
  const BearerTokenAdditional({super.key});

  @override
  State<BearerTokenAdditional> createState() => _BearerTokenAdditionalState();
}

class _BearerTokenAdditionalState extends State<BearerTokenAdditional> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(top: 10),
      child: CommonText(
        text: 'The authorization header will be automatically generated when you send the request. Learn more about authorization.',
        maxLine: 1000,
        textStyle: mRegularTextStyle16(
            textSize: 12, textColor: AppThemeColor.tableTextColor),
      ),
    );
  }
}
