import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../globals.dart';
import '../../../../../helpers/color_config.dart';
import '../../../../common_widgets/CommonTextFormField.dart';
import '../../../../common_widgets/info_icon_button.dart';
import '../bloc/auth_subviews_bloc.dart';
import '../bloc/auth_subviews_event.dart';
import 'bearer_token_data.dart';

/*class BearerToken extends StatefulWidget {
  const BearerToken({super.key});

  @override
  State<BearerToken> createState() => _BearerTokenState();
}

class _BearerTokenState extends State<BearerToken> {

  final tokenController = TextEditingController();

  @override
  void initState() {
    super.initState();
    tokenController.addListener(_updateBearerToken);
  }

  void _updateBearerToken() {
    context.read<AuthSubViewsBloc>().add(
      BearerTokenFieldsChanged(
        bearerTokenFields: BearerTokenFields(
          token: tokenController.text,
        ),
      ),
    );
  }


  @override
  void dispose() {
    tokenController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 10, right: 20, top: 10, bottom: 10),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [

          const Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(right: 8.0),
                child: InfoIconButton()
                //Icon(Icons.info_outline, size: 16, color: AppThemeColor.tabUnselectedTextColor,),
              ),
              Flexible(
                child: Text("Heads up! These parameters hold sensitive data. To keep this data secure while working in a collaborative environment, we recommend using variables.",
                  style: TextStyle(color: AppThemeColor.tabUnselectedTextColor, fontSize: 11, fontFamily: 'inter', fontWeight: FontWeight.w400),
                ),
              ),
            ],
          ),

          *//*  CommonText(
            text: 'Heads up! These parameters hold sensitive data. To keep this data secure while working in a collaborative environment, \nwe recommend using variables.',
            maxLine: 3000,
            overflow: TextOverflow.visible,
            textStyle: mRegularTextStyle16(
                textSize: 11,
                textColor: AppThemeColor.tabUnselectedTextColor),
          ),*//*

          Padding(
            padding: const EdgeInsets.only(left: 25.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CommonTextFormField(
                  labelText: "Token",
                  hintTextString: "Token",
                  isLabelText: true,
                  isLabelTextBold: true,
                  isDense: true,
                  fontSize: 14,
                  hintStyle: TextStyle(
                      color: AppThemeColor.hintTextColor),
                  labelTextSize: Globals.labelTextSizeForDialog,
                  textEditController: tokenController,
                  cornerRadius: 4,
                  errorMessage: "Enter Token",
                  padding: EdgeInsets.only(top: 20),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}*/







class BearerToken extends StatefulWidget {
  final String token;
  final ValueChanged<String> onChanged;

  const BearerToken({
    Key? key,
    required this.token,
    required this.onChanged,
  }) : super(key: key);

  @override
  State<BearerToken> createState() => _BearerTokenState();
}

class _BearerTokenState extends State<BearerToken> {
  late final TextEditingController tokenController;

  @override
  void initState() {
    super.initState();

    // Initialize controller with the provided token value
    tokenController = TextEditingController(text: widget.token);

    // Add listener to update the external state
    tokenController.addListener(() {
      widget.onChanged(tokenController.text);
    });
  }

  @override
  void dispose() {
    tokenController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 10, right: 20, top: 10, bottom: 0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          const Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(right: 8.0),
                child: InfoIconButton(),
              ),
              Flexible(
                child: Text(
                  "Heads up! These parameters hold sensitive data. To keep this data secure while working in a collaborative environment, we recommend using variables.",
                  style: TextStyle(
                    color: AppThemeColor.tabUnselectedTextColor,
                    fontSize: 11,
                    fontFamily: 'inter',
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ],
          ),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.vertical,
              child: Padding(
                padding: const EdgeInsets.only(left: 25.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonTextFormField(
                      labelText: "Token",
                      hintTextString: "Token",
                      isLabelText: true,
                      isLabelTextBold: true,
                      isDense: true,
                      fontSize: 14,
                      hintStyle: TextStyle(color: AppThemeColor.hintTextColor),
                      labelTextSize: Globals.labelTextSizeForDialog,
                      textEditController: tokenController,
                      cornerRadius: 4,
                      errorMessage: "Enter Token",
                      padding: EdgeInsets.only(top: 20),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

