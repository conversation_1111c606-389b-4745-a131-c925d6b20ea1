import 'dart:convert';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/services.dart';

class DynamicLargeResponseView extends StatefulWidget {
  final dynamic jsonData;
  final bool isDarkMode;
  final int maxLinesToRender;
  final String? searchQuery;
  final List<int>? searchMatches;
  final int? currentMatchIndex;
  final Function(List<int> matches, int currentIndex)? onSearchResultsChanged;
  final int scrollTrigger;
  final int copyTrigger;
  final VoidCallback? onCopyCompleted;

  const DynamicLargeResponseView({
    super.key,
    required this.jsonData,
    this.isDarkMode = true,
    this.maxLinesToRender = 50000, 
    this.searchQuery,
    this.searchMatches,
    this.currentMatchIndex,
    this.onSearchResultsChanged,
    this.scrollTrigger = 0,
    this.copyTrigger = 0,
    this.onCopyCompleted,
  });

  @override
  State<DynamicLargeResponseView> createState() => _DynamicLargeResponseViewState();
}

class _DynamicLargeResponseViewState extends State<DynamicLargeResponseView> {
  List<String> _lines = ['Loading...'];
  bool _isLoading = true;
  bool _isTruncated = false;
  final ScrollController _scrollController = ScrollController();
  String _fullText = '';
  List<int> _searchMatches = [];
  int _currentMatchIndex = -1;
  int _lastScrollTrigger = 0;
  int _lastCopyTrigger = 0;

  static const double _itemExtent = 18.0; // Height of each line in pixels
  static const double _overscanCount = 50.0; // Number of items to render beyond visible area

  @override
  void initState() {
    super.initState();
    _processJsonInIsolate();
  }

  @override
  void didUpdateWidget(DynamicLargeResponseView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.jsonData != widget.jsonData) {
      setState(() {
        _isLoading = true;
        _lines = ['Loading...'];
      });
      _processJsonInIsolate();
    }

    if (oldWidget.searchQuery != widget.searchQuery) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _performInternalSearch();
        }
      });
    }

    if (oldWidget.scrollTrigger != widget.scrollTrigger && widget.scrollTrigger != _lastScrollTrigger) {
      _lastScrollTrigger = widget.scrollTrigger;
      debugPrint('DynamicLargeResponseView: Scroll trigger changed from ${oldWidget.scrollTrigger} to ${widget.scrollTrigger}, currentMatchIndex: ${widget.currentMatchIndex}');
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _scrollToCurrentMatch();
        }
      });
    }

    if (oldWidget.copyTrigger != widget.copyTrigger && widget.copyTrigger != _lastCopyTrigger) {
      _lastCopyTrigger = widget.copyTrigger;
      debugPrint('DynamicLargeResponseView: Copy trigger changed from ${oldWidget.copyTrigger} to ${widget.copyTrigger}');
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _copyToClipboard();
        }
      });
    }
  }

  Future<void> _processJsonInIsolate() async {
    if (widget.jsonData == null) {
      setState(() {
        _isLoading = false;
        _lines = ['No data available'];
      });
      return;
    }

    try {
      if (kIsWeb) {
        await Future.microtask(() {
          try {
            final jsonString = const JsonEncoder.withIndent('  ').convert(widget.jsonData);
            final allLines = jsonString.split('\n');

            final isTruncated = allLines.length > widget.maxLinesToRender;
            final lines = isTruncated
                ? allLines.sublist(0, widget.maxLinesToRender)
                : allLines;

            if (mounted) {
              setState(() {
                _lines = lines;
                _isTruncated = isTruncated;
                _isLoading = false;
                _fullText = jsonString;
              });

              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  _performInternalSearch();
                }
              });
            }
          } catch (e) {
            if (mounted) {
              setState(() {
                _isLoading = false;
                _lines = ['Error formatting JSON: $e'];
              });
            }
          }
        });
      } else {
        await _processWithIsolate();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _lines = ['Error processing JSON: $e'];
        });
      }
    }
  }

  Future<void> _processWithIsolate() async {
    await Future.microtask(() {
      try {
        final jsonString = const JsonEncoder.withIndent('  ').convert(widget.jsonData);
        final allLines = jsonString.split('\n');
        final isTruncated = allLines.length > widget.maxLinesToRender;
        final lines = isTruncated
            ? allLines.sublist(0, widget.maxLinesToRender)
            : allLines;

        if (mounted) {
          setState(() {
            _lines = lines;
            _isTruncated = isTruncated;
            _isLoading = false;
            _fullText = jsonString;
          });
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              _performInternalSearch();
            }
          });
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoading = false;
            _lines = ['Error formatting JSON: $e'];
          });
        }
      }
    });
  }

  void _performInternalSearch() {
    if (widget.searchQuery == null || widget.searchQuery!.isEmpty) {
      _searchMatches.clear();
      _currentMatchIndex = -1;
      if (mounted && widget.onSearchResultsChanged != null) {
        widget.onSearchResultsChanged!([], -1);
      }
      return;
    }

    final query = widget.searchQuery!.toLowerCase();
    final text = _fullText.toLowerCase();
    final matches = <int>[];

    int index = 0;
    while (index < text.length) {
      final foundIndex = text.indexOf(query, index);
      if (foundIndex == -1) break;

      matches.add(foundIndex);
      index = foundIndex + 1;
    }

    _searchMatches = matches;
    _currentMatchIndex = matches.isNotEmpty ? 0 : -1;

    if (mounted && widget.onSearchResultsChanged != null) {
      widget.onSearchResultsChanged!(matches, _currentMatchIndex);
    }

    debugPrint('DynamicLargeResponseView: Found ${matches.length} matches for "${widget.searchQuery}"');
  }

  void _scrollToCurrentMatch() {
    final currentIndex = widget.currentMatchIndex ?? _currentMatchIndex;
    debugPrint('DynamicLargeResponseView: _scrollToCurrentMatch called - currentIndex: $currentIndex, searchMatches: ${_searchMatches.length}, lines: ${_lines.length}');

    if (currentIndex >= 0 && _searchMatches.isNotEmpty && _lines.isNotEmpty) {
      final matchPosition = _searchMatches[currentIndex];

      int targetLine = 0;
      int currentPosition = 0;

      for (int i = 0; i < _lines.length; i++) {
        final lineLength = _lines[i].length + 1; // +1 for newline
        if (currentPosition + lineLength > matchPosition) {
          targetLine = i;
          break;
        }
        currentPosition += lineLength;
      }

      final targetOffset = targetLine * _itemExtent;
      final viewportHeight = _scrollController.position.viewportDimension;
      final maxScrollExtent = _scrollController.position.maxScrollExtent;

      final centeredOffset = targetOffset - (viewportHeight / 2) + (_itemExtent / 2);
      final clampedOffset = centeredOffset.clamp(0.0, maxScrollExtent);

      debugPrint('Scrolling to line $targetLine (match ${currentIndex + 1}/${_searchMatches.length}) at offset $clampedOffset');

      _scrollController.animateTo(
        clampedOffset,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<void> _copyToClipboard() async {
    debugPrint('DynamicLargeResponseView: Copying content to clipboard');

    try {
      if (_fullText.isNotEmpty) {
        await Clipboard.setData(ClipboardData(text: _fullText));
        debugPrint('DynamicLargeResponseView: Successfully copied ${_fullText.length} characters to clipboard');

        if (widget.onCopyCompleted != null) {
          widget.onCopyCompleted!();
        }
      } else {
        debugPrint('DynamicLargeResponseView: No content to copy');
      }
    } catch (e) {
      debugPrint('DynamicLargeResponseView: Error copying to clipboard: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Column(
      children: [
        if (_isTruncated)
          Padding(
            padding: const EdgeInsets.all(0.0),
            child: Text(
              'Response is very large. Showing first ${widget.maxLinesToRender} lines.',
              style: TextStyle(
                color: widget.isDarkMode ? Colors.amber : Colors.orange,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        Expanded(
          child: Scrollbar(
            controller: _scrollController,
            child: ListView.builder(
              controller: _scrollController,
              itemCount: _lines.length,
              itemExtent: _itemExtent,
              cacheExtent: _itemExtent * _overscanCount,
              itemBuilder: (context, index) {
                if (index >= _lines.length) return const SizedBox.shrink();

                final line = _lines[index];

                return Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Line number
                    SizedBox(
                      width: 45,
                      child: Text(
                        '${index + 1}',
                        style: TextStyle(
                          color: widget.isDarkMode ? Colors.grey : Colors.grey.shade700,
                          fontSize: 11,
                          fontFamily: 'monospace',
                        ),
                        textAlign: TextAlign.right,
                      ),
                    ),
                    const SizedBox(width: 6),
                    Expanded(
                      child: _buildHighlightedText(line, index),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHighlightedText(String line, int lineIndex) {
    if (widget.searchQuery == null || widget.searchQuery!.isEmpty) {
      return Text(
        line,
        style: TextStyle(
          color: _getColorForLine(line),
          fontSize: 12,
          fontFamily: 'monospace',
        ),
      );
    }

    int lineStartPosition = 0;
    for (int i = 0; i < lineIndex; i++) {
      lineStartPosition += _lines[i].length + 1;
    }

    final List<TextSpan> spans = [];
    final searchQuery = widget.searchQuery!.toLowerCase();
    int lastIndex = 0;

    final lineMatches = <int>[];
    if (_searchMatches.isNotEmpty) {
      for (final matchPosition in _searchMatches) {
        if (matchPosition >= lineStartPosition &&
            matchPosition < lineStartPosition + line.length) {
          lineMatches.add(matchPosition - lineStartPosition);
        }
      }
    }

    for (final matchIndex in lineMatches) {
      if (matchIndex > lastIndex) {
        spans.add(TextSpan(
          text: line.substring(lastIndex, matchIndex),
          style: TextStyle(
            color: _getColorForLine(line),
            fontSize: 12,
            fontFamily: 'monospace',
          ),
        ));
      }

      final isCurrentMatch = _currentMatchIndex >= 0 &&
          _searchMatches.isNotEmpty &&
          _searchMatches[_currentMatchIndex] == lineStartPosition + matchIndex;

      spans.add(TextSpan(
        text: line.substring(matchIndex, matchIndex + searchQuery.length),
        style: TextStyle(
          color: Colors.black,
          fontSize: 12,
          backgroundColor: isCurrentMatch ? Colors.orange : Colors.yellow,
          fontFamily: 'monospace',
        ),
      ));

      lastIndex = matchIndex + searchQuery.length;
    }

    if (lastIndex < line.length) {
      spans.add(TextSpan(
        text: line.substring(lastIndex),
        style: TextStyle(
          color: _getColorForLine(line),
          fontSize: 12,
          fontFamily: 'monospace',
        ),
      ));
    }

    return RichText(
      text: TextSpan(children: spans),
    );
  }

  Color _getColorForLine(String line) {
    final trimmedLine = line.trim();

    if (widget.isDarkMode) {
      // Dark mode colors
      if (trimmedLine.startsWith('"') && trimmedLine.endsWith('",')) {
        return Colors.green; // String keys
      } else if (trimmedLine.contains('": "')) {
        return Colors.lightBlue; // String values
      } else if (trimmedLine.contains('": ') &&
                (trimmedLine.contains('true') || trimmedLine.contains('false'))) {
        return Colors.orange; // Boolean values
      } else if (trimmedLine.contains('": ') &&
                RegExp(r'": \d').hasMatch(trimmedLine)) {
        return Colors.purple.shade300; // Number values
      } else if (trimmedLine.contains('{') || trimmedLine.contains('}') ||
                trimmedLine.contains('[') || trimmedLine.contains(']')) {
        return Colors.white; // Brackets
      }
      return Colors.grey.shade300; // Default color
    } else {
      // Light mode colors
      if (trimmedLine.startsWith('"') && trimmedLine.endsWith('",')) {
        return Colors.green.shade800; // String keys
      } else if (trimmedLine.contains('": "')) {
        return Colors.blue.shade800; // String values
      } else if (trimmedLine.contains('": ') &&
                (trimmedLine.contains('true') || trimmedLine.contains('false'))) {
        return Colors.orange.shade800; // Boolean values
      } else if (trimmedLine.contains('": ') &&
                RegExp(r'": \d').hasMatch(trimmedLine)) {
        return Colors.purple.shade800; // Number values
      } else if (trimmedLine.contains('{') || trimmedLine.contains('}') ||
                trimmedLine.contains('[') || trimmedLine.contains(']')) {
        return Colors.black; // Brackets
      }
      return Colors.grey.shade800; // Default color
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}
