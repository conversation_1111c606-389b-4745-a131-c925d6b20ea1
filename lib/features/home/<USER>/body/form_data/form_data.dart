import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../helpers/color_config.dart';
import '../../../../../helpers/style_config.dart';
import '../../../../common_widgets/CommonText.dart';
import '../../../../common_widgets/custom_checkbox.dart';
import '../../header/bloc/headers_bloc.dart';
import '../../header/bloc/headers_event.dart';
import '../../header/bloc/headers_state.dart';
import 'bloc/form_data_bloc.dart';
import 'bloc/form_data_event.dart';
import 'bloc/form_data_state.dart';

/*class FormData extends StatefulWidget {
  const FormData({super.key});

  @override
  State<FormData> createState() => _FormDataState();
}

class _FormDataState extends State<FormData> {

  List<Map<String, TextEditingController>> rows = [];
  List<double> columnWidths = [1.0, 1.0, 2.0, 1.0];
  int? hoveredColumnIndex;
  bool isDragging = false;

  @override
  void initState() {
    super.initState();
    _addNewRow();
  }

  void _addNewRow() {
    setState(() {
      rows.add({
        'Key': TextEditingController(),
        'Value': TextEditingController(),
        'Description': TextEditingController(),
        //'BulkEdit': TextEditingController(),
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          //height: 180,
          padding: EdgeInsets.only(bottom: 10, right: 20),
          constraints: new BoxConstraints(
            minHeight: 200.0,
            maxHeight: 400.0,
          ),
          child: SingleChildScrollView(
            scrollDirection: Axis.vertical,
            child: Table(
              border: TableBorder.all(color: AppThemeColor.commonBorderColor),
              columnWidths: {
                0: FlexColumnWidth(columnWidths[0]),
                1: FlexColumnWidth(columnWidths[1]),
                2: FlexColumnWidth(columnWidths[2]),
                3: FlexColumnWidth(columnWidths[3]),
              },
              defaultVerticalAlignment: TableCellVerticalAlignment.middle,
              children: [
                _buildHeaderRow(),
                ..._buildDataRows(),
              ],
            ),
          ),
        ),
    ],);
  }

  TableRow _buildHeaderRow() {
    return TableRow(
      children: [
        _buildResizableHeaderCell('Key', 0),
        _buildResizableHeaderCell('Value', 1),
        _buildResizableHeaderCell('Description', 2),
        //_buildResizableHeaderCell('Bulk Edit', 3),
      ],
    );
  }

  List<TableRow> _buildDataRows() {
    return List.generate(rows.length, (index) {
      return TableRow(
        children: [
          _buildTextFieldCell(index, 'Key', TextAlign.left, 35),
          _buildTextFieldCell(index, 'Value', TextAlign.left, 35),
          _buildTextFieldCell(index, 'Description', TextAlign.left, 35),
          //_buildTextFieldCell(index, 'BulkEdit', TextAlign.left, 35),
        ],
      );
    });
  }

  Widget _buildResizableHeaderCell(String text, int columnIndex) {
    return MouseRegion(
      onEnter: (_) {
        setState(() {
          hoveredColumnIndex = columnIndex;
        });
      },
      onExit: (_) {
        setState(() {
          hoveredColumnIndex = null;
        });
      },
      child: Stack(
        children: [
          Container(
            height: 35,
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.symmetric(horizontal: 12.0),
            color: AppThemeColor.commonBackground,
            child: CommonText(
              text: text,
              textAlign: TextAlign.left,
              textStyle: mMediumTextStyle16(
                  textSize: 14, textColor: AppThemeColor.tableHeaderTextColor),
            ),
          ),
          if (hoveredColumnIndex == columnIndex || isDragging)
            Positioned(
              right: -10,
              top: 0,
              bottom: 0,
              child: GestureDetector(
                onHorizontalDragStart: (_) {
                  setState(() {
                    isDragging = true;
                  });
                },
                onHorizontalDragUpdate: (details) {
                  setState(() {
                    columnWidths[columnIndex] += details.delta.dx * 0.01;
                    columnWidths[columnIndex] =
                        columnWidths[columnIndex].clamp(0.5, 5.0);
                  });
                },
                onHorizontalDragEnd: (_) {
                  setState(() {
                    isDragging = false;
                  });
                },
                child: MouseRegion(
                  cursor: SystemMouseCursors.resizeColumn,
                  child: Container(
                    width: 20,
                    alignment: Alignment.center,
                    child: Icon(
                      Icons.drag_handle,
                      size: 16,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTextFieldCell(
      int rowIndex, String fieldName, TextAlign alignment, double height) {
    return Container(
      height: height,
      padding: const EdgeInsets.symmetric(horizontal: 12.0),
      alignment: _getAlignmentFromTextAlign(alignment),
      child: Center(
        child: SizedBox(
          height: height - 10,
          child: TextField(
            controller: rows[rowIndex][fieldName],
            textAlign: alignment,
            style: TextStyle(color: AppThemeColor.tableHeaderTextColor, fontSize: 12),
            decoration: InputDecoration(
              contentPadding: EdgeInsets.zero,
              border: OutlineInputBorder(
                borderSide: BorderSide.none,
              ),
              hintText: fieldName,
              hintStyle: TextStyle(color: AppThemeColor.tableTextColor, fontSize: 12),
            ),
            onChanged: (value) {
              if (rowIndex == rows.length - 1) {
                _addNewRow();
              }
            },
          ),
        ),
      ),
    );
  }

  Alignment _getAlignmentFromTextAlign(TextAlign align) {
    switch (align) {
      case TextAlign.left:
        return Alignment.centerLeft;
      case TextAlign.center:
        return Alignment.center;
      case TextAlign.right:
        return Alignment.centerRight;
      default:
        return Alignment.centerLeft;
    }
  }

}*/




//with bloc
class FormData extends StatefulWidget {
  const FormData({super.key});

  @override
  State<FormData> createState() => _FormDataState();
}

class _FormDataState extends State<FormData> {

  int? hoveredColumnIndex;
  bool isDragging = false;
  List<double> columnWidths = [1.0, 1.0, 2.0];

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FormDataBloc, FormDataState>(
      builder: (context, state) {
        return SingleChildScrollView(
          scrollDirection: Axis.vertical,
          child: Table(
            border: TableBorder.all(
                color: AppThemeColor.commonBorderColor),
            columnWidths: {
              0: const FixedColumnWidth(40),
              1: FlexColumnWidth(columnWidths[0]),
              2: FlexColumnWidth(columnWidths[1]),
              3: FlexColumnWidth(columnWidths[2]),
            },
            defaultVerticalAlignment: TableCellVerticalAlignment.middle,
            children: [
              _buildHeaderRow(),
              ..._buildDataRows(context, state),
            ],
          ),
        );
      },
    );
  }

  TableRow _buildHeaderRow() {
    return TableRow(
      children: [
        Container(),
        _buildResizableHeaderCell('Key', 0),
        _buildResizableHeaderCell('Value', 1),
        _buildResizableHeaderCell('Description', 2),
      ],
    );
  }

  Widget _buildResizableHeaderCell(String text, int columnIndex) {
    return MouseRegion(
      onEnter: (_) {
        setState(() {
          hoveredColumnIndex = columnIndex;
        });
      },
      onExit: (_) {
        setState(() {
          hoveredColumnIndex = null;
        });
      },
      child: Stack(
        children: [
          Container(
            height: 35,
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.symmetric(horizontal: 12.0),
            color: AppThemeColor.commonBackground,
            child: CommonText(
              text: text,
              textAlign: TextAlign.left,
              textStyle: mMediumTextStyle16(
                  textSize: 14, textColor: AppThemeColor.tableHeaderTextColor),
            ),
          ),
          if (hoveredColumnIndex == columnIndex || isDragging)
            Positioned(
              right: -10,
              top: 0,
              bottom: 0,
              child: GestureDetector(
                onHorizontalDragStart: (_) {
                  setState(() {
                    isDragging = true;
                  });
                },
                onHorizontalDragUpdate: (details) {
                  setState(() {
                    columnWidths[columnIndex] += details.primaryDelta! * 0.01;
                    columnWidths[columnIndex] =
                        columnWidths[columnIndex].clamp(0.5, 5.0);
                  });
                },
                onHorizontalDragEnd: (_) {
                  setState(() {
                    isDragging = false;
                  });
                },
                child: MouseRegion(
                  cursor: SystemMouseCursors.resizeColumn,
                  child: Container(
                    width: 20,
                    alignment: Alignment.center,
                    child: Icon(
                      Icons.drag_handle,
                      size: 16,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }


  List<TableRow> _buildDataRows(BuildContext context, FormDataState state) {
    return List.generate(state.rows.length, (index) {
      final row = state.rows[index];
      return TableRow(
        children: [
          _buildCheckboxCell(context, index, row.isSelected),
          _buildTextFieldCell(
              context, index, row.keyController, 'key', isStatic: row.isStatic),
          _buildTextFieldCell(context, index, row.valueController, 'value'),
          _buildTextFieldCell(
              context, index, row.descriptionController, 'description'),
        ],
      );
    });
  }

  Widget _buildTextFieldCell(BuildContext context,
      int rowIndex,
      TextEditingController controller,
      String fieldName, {
        bool isStatic = false,
      }) {
    return Container(
      height: 35,
      padding: const EdgeInsets.symmetric(horizontal: 12.0),
      alignment: Alignment.centerLeft,
      child: TextField(
        controller: controller,
        textAlign: TextAlign.left,
        style: TextStyle(
            color: AppThemeColor.tableHeaderTextColor, fontSize: 12),
        decoration: InputDecoration(
          contentPadding: EdgeInsets.zero,
          border: const OutlineInputBorder(
            borderSide: BorderSide.none,
          ),
          hintText: fieldName,
          hintStyle: TextStyle(
              color: AppThemeColor.tableTextColor, fontSize: 12),
        ),
        onChanged: (value) {
          final formDataBloc = BlocProvider.of<FormDataBloc>(context);
          final row = formDataBloc.state.rows[rowIndex];

          formDataBloc.add(
            UpdateFormDataRowEvent(
              rowIndex: rowIndex,
              key: fieldName == 'key' ? value : row.key,
              value: fieldName == 'value' ? value : row.value,
              description: fieldName == 'description' ? value : row.description,
            ),
          );
        },
      ),
    );
  }

  Widget _buildCheckboxCell(BuildContext context, int rowIndex,
      bool isSelected) {
    return Center(
      child: CustomCheckbox(
        value: isSelected,
        label: "",
        checkboxSize: 20,
        checkIconSize: 14,
        onChanged: (value) {
          BlocProvider.of<FormDataBloc>(context).add(
            FormDataToggleRowSelectionEvent(rowIndex: rowIndex, isSelected: value!),
          );
        },
      ),
    );
  }

}

