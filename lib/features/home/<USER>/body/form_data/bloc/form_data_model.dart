import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';



class FormDataRow {
  final TextEditingController keyController;
  final TextEditingController valueController;
  final TextEditingController descriptionController;
  String key;
  String value;
  String description;
  bool isSelected;
  final bool isStatic;

  FormDataRow({
    required this.keyController,
    required this.valueController,
    required this.descriptionController,
    this.key = '',
    this.value = '',
    this.description = '',
    this.isSelected = true, // Default to checked
    this.isStatic = false, // Indicates if the row is static
  });

  FormDataRow copyWith({
    String? key,
    String? value,
    String? description,
    bool? isSelected,
    bool? isStatic,
  }) {
    return FormDataRow(
      keyController: this.keyController,
      valueController: this.valueController,
      descriptionController: this.descriptionController,
      key: key ?? this.key,
      value: value ?? this.value,
      description: description ?? this.description,
      isSelected: isSelected ?? this.isSelected,
      isStatic: isStatic ?? this.isStatic,
    );
  }
}




//working code
/*class HeaderRow {
  final TextEditingController keyController;
  final TextEditingController valueController;
  final TextEditingController descriptionController;
  String key;
  String value;
  String description;
  bool isSelected;

  HeaderRow({
    required this.keyController,
    required this.valueController,
    required this.descriptionController,
    this.key = '',
    this.value = '',
    this.description = '',
    this.isSelected = true,
  });

  HeaderRow copyWith({
    String? key,
    String? value,
    String? description,
    bool? isSelected,
  }) {
    return HeaderRow(
      keyController: this.keyController,
      valueController: this.valueController,
      descriptionController: this.descriptionController,
      key: key ?? this.key,
      value: value ?? this.value,
      description: description ?? this.description,
      isSelected: isSelected ?? this.isSelected,
    );
  }
}*/







