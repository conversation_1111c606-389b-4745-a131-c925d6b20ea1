import 'package:bloc/bloc.dart';
import 'package:flutter/cupertino.dart';

import 'form_data_event.dart';
import 'form_data_model.dart';
import 'form_data_state.dart';


class FormDataBloc extends Bloc<FormDataEvent, FormDataState> {
  FormDataBloc()
      : super(FormDataState(rows: [])) {
    on<AddFormDataRowEvent>((event, emit) {
      final newRow = FormDataRow(
        keyController: TextEditingController(),
        valueController: TextEditingController(),
        descriptionController: TextEditingController(),
      );
      final newRows = List<FormDataRow>.from(state.rows)..add(newRow);
      emit(state.copyWith(rows: newRows));
    });

    on<UpdateFormDataRowEvent>((event, emit) {
      final updatedRows = List<FormDataRow>.from(state.rows);
      final row = updatedRows[event.rowIndex];


      if (!row.isStatic || event.key == row.key) {
        updatedRows[event.rowIndex] = row.copyWith(
          key: event.key,
          value: event.value,
          description: event.description,
        );
      }

      emit(state.copyWith(rows: updatedRows));

      if (event.rowIndex == updatedRows.length - 1) {
        add(AddFormDataRowEvent());
      }
    });

    on<FormDataToggleRowSelectionEvent>((event, emit) {
      final updatedRows = List<FormDataRow>.from(state.rows);
      updatedRows[event.rowIndex] =
          updatedRows[event.rowIndex].copyWith(isSelected: event.isSelected);
      emit(state.copyWith(rows: updatedRows));
    });
  }
}






//working code
/*class HeadersBloc extends Bloc<HeadersEvent, HeadersState> {
  HeadersBloc() : super(const HeadersState(rows: [])) {
    on<AddRowEvent>((event, emit) {
      final newRow = HeaderRow(
        keyController: TextEditingController(),
        valueController: TextEditingController(),
        descriptionController: TextEditingController(),
      );
      final newRows = List<HeaderRow>.from(state.rows)..add(newRow);
      emit(state.copyWith(rows: newRows));
    });

    on<UpdateRowEvent>((event, emit) {
      final updatedRows = List<HeaderRow>.from(state.rows);
      final row = updatedRows[event.rowIndex];

      updatedRows[event.rowIndex] = row.copyWith(
        key: event.key,
        value: event.value,
        description: event.description,
      );

      emit(state.copyWith(rows: updatedRows));

      if (event.rowIndex == updatedRows.length - 1) {
        add(AddRowEvent());
      }
    });

    on<ToggleRowSelectionEvent>((event, emit) {
      final updatedRows = List<HeaderRow>.from(state.rows);
      updatedRows[event.rowIndex] =
          updatedRows[event.rowIndex].copyWith(isSelected: event.isSelected);
      emit(state.copyWith(rows: updatedRows));
    });
  }
}*/



