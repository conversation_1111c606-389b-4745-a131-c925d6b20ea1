
import 'package:equatable/equatable.dart';

import 'form_data_model.dart';

abstract class FormDataEvent extends Equatable {
  @override
  List<Object> get props => [];
}

class AddFormDataRowEvent extends FormDataEvent {}

class UpdateFormDataRowEvent extends FormDataEvent {
  final int rowIndex;
  final String key;
  final String value;
  final String description;

  UpdateFormDataRowEvent({
    required this.rowIndex,
    required this.key,
    required this.value,
    required this.description,
  });

  @override
  List<Object> get props => [rowIndex, key, value, description];
}

class FormDataToggleRowSelectionEvent extends FormDataEvent {
  final int rowIndex;
  final bool isSelected;

  FormDataToggleRowSelectionEvent({required this.rowIndex, required this.isSelected});

  @override
  List<Object> get props => [rowIndex, isSelected];
}


// Bloc: Handles the logic for adding/updating rows





