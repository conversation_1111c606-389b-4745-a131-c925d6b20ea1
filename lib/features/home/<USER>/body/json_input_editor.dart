import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_code_editor/flutter_code_editor.dart';
import 'package:flutter_highlight/themes/a11y-light.dart';

import '../../../../helpers/color_config.dart';
import '../../../common_widgets/dynamic_text_area.dart';

class JsonInputEditor extends StatefulWidget {
  const JsonInputEditor({
    super.key,
    //required this.body,
    //required this.onChanged,
    required this.jsonController,
  });

  //final String body;
  //final Function(String) onChanged;
  final CodeController jsonController;

  @override
  JsonInputEditorState createState() => JsonInputEditorState();
}

class JsonInputEditorState extends State<JsonInputEditor> {

  late String body;
  late Function(String) onChanged;
  late CodeController jsonController;

  @override
  Widget build(BuildContext context) {
    return Flexible(
      child: CodeField(
        //onChanged: onChanged,
        gutterStyle: GutterStyle.none,
        controller: json<PERSON>ontroller,
        expands: true,
        wrap: true,
        textStyle: const TextStyle(
          fontSize: 14,
        ),
      ),
    );
  }
}