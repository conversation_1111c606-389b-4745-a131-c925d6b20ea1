
import 'dart:convert' as dc;
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_code_editor/flutter_code_editor.dart';
import 'package:flutter_highlight/themes/a11y-dark.dart';
import 'package:flutter_highlight/themes/atom-one-light.dart';
import 'package:postman_flutter/features/home/<USER>/api_collection.dart';
import '../../../../../helpers/color_config.dart';
import '../../../../../helpers/style_config.dart';
import '../../../../../helpers/debouncer.dart';
import '../../../../common_widgets/dynamic_text_area.dart';
import '../../../bloc/home_bloc.dart';
import '../../../bloc/home_event.dart';
import '../../../tab_manager.dart';
import '../json_input_editor.dart';
import 'package:highlight/languages/json.dart' show json;
import 'bloc/json_data_bloc.dart';
import 'bloc/json_data_event.dart';
import 'bloc/json_data_state.dart';



class JsonDataView extends StatefulWidget {

  final TextEditingController jsonController;
  final TabModel? tabModel;

  JsonDataView({required this.jsonController,this.tabModel, super.key});

  @override
  _JsonDataViewState createState() => _JsonDataViewState();
}

class _JsonDataViewState extends State<JsonDataView> {
  late final CodeController _codeController;

  @override
  void initState() {
    super.initState();
    _codeController = CodeController(
      text: widget.jsonController.text,
      language: json,
    );

    var debouncer = Debouncer(milliseconds: 300);

    _codeController.addListener(() {
      debouncer.run(() {
        var newData = _codeController.text;
        if (newData.isNotEmpty) {
          Future.microtask(() {
            try {
              Map<String, dynamic> jsonn = dc.json.decode(newData);
              if (mounted) {
                widget.tabModel?.jsonData = jsonn;
                context.read<JsonDataBloc>().add(UpdateJsonDataEvent(jsonn));
                context.read<HomeBloc>().add(UpdateJsonDataEventTab(jsonn));
              }
            } catch (e) {
              debugPrint("JSON parsing error: $e");
            }
          });
        }
      });
    });

  }

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return  Column(
      children: [
        Container(
          constraints: const BoxConstraints(minHeight: 200, maxHeight: 400),
          child: CodeTheme(
            data: CodeThemeData(styles: a11yDarkTheme),
            child: CodeField(
              background: AppThemeColor.commonBackground,
              controller: _codeController,
              expands: true,
              wrap: true,
              textStyle: mRegularTextStyle16(textSize: 12),
            ),
          ),
        ),
      ],
    );
  }
}