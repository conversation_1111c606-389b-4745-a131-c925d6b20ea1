import 'dart:convert';

import 'package:bloc/bloc.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';

import 'json_data_event.dart';
import 'json_data_state.dart';

class JsonDataBloc extends Bloc<JsonDataEvent, JsonDataState> {
  JsonDataBloc() : super(const JsonDataState(null)) {
    on<UpdateJsonDataEvent>(_updateJsonData);
  }


  Future<void> _updateJsonData(UpdateJsonDataEvent event, Emitter<JsonDataState> emit) async {
   emit(state.copyWith(jsonData: event.newJson));
  }
}

