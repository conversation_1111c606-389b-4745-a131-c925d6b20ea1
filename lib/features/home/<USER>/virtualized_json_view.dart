import 'dart:convert';
import 'package:flutter/material.dart';

/// A virtualized JSON viewer that efficiently renders large JSON data
/// by only rendering the visible portion of the data
class VirtualizedJsonView extends StatefulWidget {
  final Map<String, dynamic>? jsonData;
  final String? jsonString;
  final bool isDarkMode;

  const VirtualizedJsonView({
    super.key,
    this.jsonData,
    this.jsonString,
    this.isDarkMode = true,
  });

  @override
  State<VirtualizedJsonView> createState() => _VirtualizedJsonViewState();
}

class _VirtualizedJsonViewState extends State<VirtualizedJsonView> {
  late List<String> _lines;
  final ScrollController _scrollController = ScrollController();

  // Constants for virtualization
  static const double _itemExtent = 20.0; // Height of each line in pixels
  static const double _overscanCount = 20.0; // Number of items to render beyond visible area

  @override
  void initState() {
    super.initState();
    _parseJsonToLines();
  }

  @override
  void didUpdateWidget(VirtualizedJsonView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.jsonData != widget.jsonData ||
        oldWidget.jsonString != widget.jsonString) {
      _parseJsonToLines();
    }
  }

  void _parseJsonToLines() {
    String jsonStr = widget.jsonString ?? '';

    if (widget.jsonData != null) {
      // Format the JSON data with indentation
      try {
        // First set a loading state
        setState(() {
          _lines = ['Loading JSON data...'];
        });

        // Use a microtask to avoid blocking the UI
        Future.microtask(() {
          try {
            // For small JSON, process directly
            final jsonString = const JsonEncoder.withIndent('  ').convert(widget.jsonData!);
            final lines = jsonString.split('\n');

            if (mounted) {
              setState(() {
                _lines = lines;
              });
            }
          } catch (e) {
            if (mounted) {
              setState(() {
                _lines = ['Error formatting JSON: $e'];
              });
            }
          }
        });
      } catch (e) {
        _lines = ['Error formatting JSON: $e'];
      }
    } else if (jsonStr.isNotEmpty) {
      _lines = jsonStr.split('\n');
    } else {
      _lines = ['No data available'];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scrollbar(
      controller: _scrollController,
      child: ListView.builder(
        controller: _scrollController,
        itemCount: _lines.length,
        itemExtent: _itemExtent, // Fixed height for each item improves performance
        cacheExtent: _itemExtent * _overscanCount, // Cache items beyond visible area
        itemBuilder: (context, index) {
          if (index >= _lines.length) return const SizedBox.shrink();

          final line = _lines[index];

          return Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Line number
              SizedBox(
                width: 40,
                child: Text(
                  '${index + 1}',
                  style: TextStyle(
                    color: widget.isDarkMode ? Colors.grey : Colors.grey.shade700,
                    fontFamily: 'monospace',
                  ),
                  textAlign: TextAlign.right,
                ),
              ),
              const SizedBox(width: 8),
              // Line content with syntax highlighting
              Expanded(
                child: Text(
                  line,
                  style: TextStyle(
                    color: _getColorForLine(line),
                    fontFamily: 'monospace',
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Color _getColorForLine(String line) {
    final trimmedLine = line.trim();

    if (widget.isDarkMode) {
      // Dark mode colors
      if (trimmedLine.startsWith('"') && trimmedLine.endsWith('",')) {
        return Colors.green; // String keys
      } else if (trimmedLine.contains('": "')) {
        return Colors.lightBlue; // String values
      } else if (trimmedLine.contains('": ') &&
                (trimmedLine.contains('true') || trimmedLine.contains('false'))) {
        return Colors.orange; // Boolean values
      } else if (trimmedLine.contains('": ') &&
                RegExp(r'": \d').hasMatch(trimmedLine)) {
        return Colors.purple.shade300; // Number values
      } else if (trimmedLine.contains('{') || trimmedLine.contains('}') ||
                trimmedLine.contains('[') || trimmedLine.contains(']')) {
        return Colors.white; // Brackets
      }
      return Colors.grey.shade300; // Default color
    } else {
      // Light mode colors
      if (trimmedLine.startsWith('"') && trimmedLine.endsWith('",')) {
        return Colors.green.shade800; // String keys
      } else if (trimmedLine.contains('": "')) {
        return Colors.blue.shade800; // String values
      } else if (trimmedLine.contains('": ') &&
                (trimmedLine.contains('true') || trimmedLine.contains('false'))) {
        return Colors.orange.shade800; // Boolean values
      } else if (trimmedLine.contains('": ') &&
                RegExp(r'": \d').hasMatch(trimmedLine)) {
        return Colors.purple.shade800; // Number values
      } else if (trimmedLine.contains('{') || trimmedLine.contains('}') ||
                trimmedLine.contains('[') || trimmedLine.contains(']')) {
        return Colors.black; // Brackets
      }
      return Colors.grey.shade800; // Default color
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}
