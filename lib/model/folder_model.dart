import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:postman_flutter/model/collection_detail_model.dart';

FolderResponse folderResponseFromJson(String str) => FolderResponse.fromJson(json.decode(str));

String folderResponseToJson(FolderResponse data) => json.encode(data.toJson());

class FolderResponse {
  String? status;
  String? msg;
  dynamic data; // Can be a single folder or a list of folders

  FolderResponse({
    this.status,
    this.msg,
    this.data,
  });

  factory FolderResponse.fromJson(Map<String, dynamic> json) {
    return FolderResponse(
      status: json["status"],
      msg: json["msg"],
      data: json["data"],
    );
  }

  // Helper method to get a single folder
  FolderModel? getSingleFolder() {
    if (data is Map<String, dynamic>) {
      return FolderModel.fromJson(data);
    }
    return null;
  }

  Map<String, dynamic> toJson() => {
    "status": status,
    "msg": msg,
    "data": data,
  };
}

class FolderModel {
  int? id;
  String? name;
  String? description;
  int? collectionId;
  int? parentFolderId;
  int? order;
  int? isActive;
  int? isDelete;

  FolderModel({
    this.id,
    this.name,
    this.description,
    this.collectionId,
    this.parentFolderId,
    this.order,
    this.isActive,
    this.isDelete,
  });

  factory FolderModel.fromJson(Map<String, dynamic> json) => FolderModel(
    id: json["id"],
    name: json["name"],
    description: json["description"],
    collectionId: json["collection_id"],
    parentFolderId: json["parent_folder_id"],
    order: json["order"],
    isActive: json["is_active"],
    isDelete: json["is_delete"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "description": description,
    "collection_id": collectionId,
    "parent_folder_id": parentFolderId,
    "order": order,
    "is_active": isActive,
    "is_delete": isDelete,
  };

  Folder toFolder() {
    debugPrint('Converting FolderModel to Folder: id=$id, name=$name');

    return Folder(
      id: id,
      name: name,
      description: description,
      collectionId: collectionId,
      parentFolderId: parentFolderId,
      order: order,
      isActive: isActive,
      folders: [],
      requests: [],
    );
  }
}
