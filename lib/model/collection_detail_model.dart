import 'dart:convert';
import 'package:flutter/foundation.dart';

class CollectionDetailResponse {
  String? status;
  String? msg;
  CollectionDetail? data;

  CollectionDetailResponse({
    this.status,
    this.msg,
    this.data,
  });

  factory CollectionDetailResponse.fromJson(Map<String, dynamic> json) => CollectionDetailResponse(
    status: json["status"],
    msg: json["msg"],
    data: json["data"] == null ? null : CollectionDetail.fromJson(json["data"]),
  );

  Map<String, dynamic> toJson() => {
    "status": status,
    "msg": msg,
    "data": data?.toJson(),
  };
}

class CollectionDetail {
  int? id;
  String? name;
  String? description;
  int? userId;
  int? workspaceId;
  int? isActive;
  DateTime? createdAt;
  DateTime? updatedAt;
  List<Request>? requests;
  List<Folder>? folders;

  CollectionDetail({
    this.id,
    this.name,
    this.description,
    this.userId,
    this.workspaceId,
    this.isActive,
    this.createdAt,
    this.updatedAt,
    this.requests,
    this.folders,
  });

  factory CollectionDetail.fromJson(Map<String, dynamic> json) => CollectionDetail(
    id: json["id"],
    name: json["name"],
    description: json["description"],
    userId: json["user_id"],
    workspaceId: json["workspace_id"],
    isActive: json["is_active"],
    createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    requests: json["requests"] == null ? [] : List<Request>.from(json["requests"].map((x) => Request.fromJson(x))),
    folders: json["folders"] == null ? [] : List<Folder>.from(json["folders"].map((x) => Folder.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "description": description,
    "user_id": userId,
    "workspace_id": workspaceId,
    "is_active": isActive,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "requests": requests == null ? [] : List<dynamic>.from(requests!.map((x) => x.toJson())),
    "folders": folders == null ? [] : List<dynamic>.from(folders!.map((x) => x.toJson())),
  };
}

class Folder {
  int? id;
  String? name;
  String? description;
  int? collectionId;
  int? parentFolderId;
  int? order;
  int? isActive;
  DateTime? createdAt;
  DateTime? updatedAt;
  List<Folder>? folders;
  List<Request>? requests;

  Folder({
    this.id,
    this.name,
    this.description,
    this.collectionId,
    this.parentFolderId,
    this.order,
    this.isActive,
    this.createdAt,
    this.updatedAt,
    this.folders,
    this.requests,
  });

  factory Folder.fromJson(Map<String, dynamic> json) {
    // Check if this is the alternative format with collection_folder_ prefix
    final bool isAlternativeFormat = json.containsKey("collection_folder_Id");

    return Folder(
      // Handle both standard and alternative formats
      id: isAlternativeFormat ? json["collection_folder_Id"] : json["id"],
      name: isAlternativeFormat ? json["collection_folder_Name"] : json["name"],
      description: isAlternativeFormat ? json["collection_folder_Description"] : json["description"],
      collectionId: isAlternativeFormat ? json["collection_Id"] : json["collection_id"],
      parentFolderId: isAlternativeFormat ? json["collection_folder_Parent_Id"] : json["parent_folder_id"],
      order: isAlternativeFormat ? json["collection_folder_Order"] : json["order"],
      isActive: json["is_active"],
      createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
      updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
      folders: json["folders"] == null ? [] : List<Folder>.from(json["folders"].map((x) => Folder.fromJson(x))),
      requests: json["requests"] == null ? [] : List<Request>.from(json["requests"].map((x) => Request.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() {
    // For debugging purposes
    debugPrint('Converting Folder to JSON: id=$id, name=$name');

    return {
      "id": id,
      "name": name,
      "description": description,
      "collection_id": collectionId,
      "parent_folder_id": parentFolderId,
      "order": order,
      "is_active": isActive,
      "created_at": createdAt?.toIso8601String(),
      "updated_at": updatedAt?.toIso8601String(),
      "folders": folders == null ? [] : List<dynamic>.from(folders!.map((x) => x.toJson())),
      "requests": requests == null ? [] : List<dynamic>.from(requests!.map((x) => x.toJson())),
    };
  }
}

class Request {
  int? id;
  String? name;
  String? url;
  String? method;
  Map<String, dynamic>? headers;
  Body? body;
  Map<String, dynamic>? params;
  dynamic authType;
  int? collectionId;
  dynamic folderId;
  int? isActive;
  DateTime? createdAt;
  DateTime? updatedAt;

  Request({
    this.id,
    this.name,
    this.url,
    this.method,
    this.headers,
    this.body,
    this.params,
    this.authType,
    this.collectionId,
    this.folderId,
    this.isActive,
    this.createdAt,
    this.updatedAt,
  });

  factory Request.fromJson(Map<String, dynamic> json) => Request(
    id: json["id"],
    name: json["name"],
    url: json["url"],
    method: json["method"],
    headers: json["headers"] is Map ? Map<String, dynamic>.from(json["headers"]) : {},
    body: json["body"] == null ? null : Body.fromJson(json["body"]),
    params: json["params"] is Map ? Map<String, dynamic>.from(json["params"]) : {},
    authType: json["auth_type"],
    collectionId: json["collection_id"],
    folderId: json["folder_id"],
    isActive: json["is_active"],
    createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "url": url,
    "method": method,
    "headers": headers,
    "body": body?.toJson(),
    "params": params,
    "auth_type": authType,
    "collection_id": collectionId,
    "folder_id": folderId,
    "is_active": isActive,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
  };
}

class Body {
  String? type;
  dynamic content;

  Body({
    this.type,
    this.content,
  });

  factory Body.fromJson(Map<String, dynamic> json) => Body(
    type: json["type"],
    content: json["content"],
  );

  Map<String, dynamic> toJson() => {
    "type": type,
    "content": content,
  };
}
