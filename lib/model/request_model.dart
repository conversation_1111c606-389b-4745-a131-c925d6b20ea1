
class PostmanRequestResponse {
  final String status;
  final String msg;
  final RequestModel? data;

  PostmanRequestResponse({
    required this.status,
    required this.msg,
     this.data,
  });

  factory PostmanRequestResponse.fromJson(Map<String, dynamic> json) {
    return PostmanRequestResponse(
      status: json['status'],
      msg: json['msg'],
      data: RequestModel.fromJson(json['data']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'msg': msg,
      'data': data?.toJson(),
    };
  }
}

class RequestModel {
  final int id;
  final String name;
  final String url;
  final String method;
  final Map<String, dynamic> headers;
  final Map<String, dynamic> body;
  final Map<String, dynamic> params;
  final int collectionId;
  final int order;
  final int isActive;
  final int isDelete;

  RequestModel({
    required this.id,
    required this.name,
    required this.url,
    required this.method,
    required this.headers,
    required this.body,
    required this.params,
    required this.collectionId,
    required this.order,
    required this.isActive,
    required this.isDelete,
  });

  factory RequestModel.fromJson(Map<String, dynamic> json) {
    return RequestModel(
      id: json['id'],
      name: json['name'],
      url: json['url'],
      method: json['method'],
      headers: Map<String, dynamic>.from(json['headers']),
      body: Map<String, dynamic>.from(json['body']),
      params: Map<String, dynamic>.from(json['params']),
      collectionId: json['collection_id'],
      order: json['order'],
      isActive: json['is_active'],
      isDelete: json['is_delete'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'url': url,
      'method': method,
      'headers': headers,
      'body': body,
      'params': params,
      'collection_id': collectionId,
      'order': order,
      'is_active': isActive,
      'is_delete': isDelete,
    };
  }
}


