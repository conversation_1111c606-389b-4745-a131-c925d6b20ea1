// To parse this JSON data, do
//
//     final collectionResponse = collectionResponseFromJson(jsonString);

import 'dart:convert';

CollectionResponse collectionResponseFromJson(String str) => CollectionResponse.fromJson(json.decode(str));

String collectionResponseToJson(CollectionResponse data) => json.encode(data.toJson());

class CollectionResponse {
    String? status;
    String? msg;
    dynamic data; // Can be a list of collections or a single collection object

    CollectionResponse({
        this.status,
        this.msg,
        this.data,
    });

    factory CollectionResponse.fromJson(Map<String, dynamic> json) {
        return CollectionResponse(
            status: json["status"],
            msg: json["msg"],
            data: json["data"],
        );
    }

    // Helper method to get collections list
    List<Collection> getCollections() {
        if (data == null) {
            return [];
        } else if (data is List) {
            return List<Collection>.from(data.map((x) => Collection.fromJson(x)));
        } else if (data is Map<String, dynamic>) {
            // Single collection case
            return [Collection.fromJson(data)];
        }
        return [];
    }

    // Helper method to get a single collection
    Collection? getSingleCollection() {
        if (data is Map<String, dynamic>) {
            return Collection.fromJson(data);
        }
        return null;
    }

    Map<String, dynamic> toJson() => {
        "status": status,
        "msg": msg,
        "data": data,
    };
}

class Collection {
    int? collectionId;
    String? collectionTitle;
    String? collectionDescription;
    int? loginUserId;
    int? cmsWorkspaceId;
    int? isActive;
    DateTime? createdAt;
    DateTime? updatedAt;

    Collection({
        this.collectionId,
        this.collectionTitle,
        this.collectionDescription,
        this.loginUserId,
        this.cmsWorkspaceId,
        this.isActive,
        this.createdAt,
        this.updatedAt,
    });

    factory Collection.fromJson(Map<String, dynamic> json) => Collection(
        collectionId: json["collection_Id"],
        collectionTitle: json["collection_Title"],
        collectionDescription: json["collection_Description"],
        loginUserId: json["login_user_Id"],
        cmsWorkspaceId: json["cms_workspace_Id"],
        isActive: json["is_active"],
        createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    );

    Map<String, dynamic> toJson() => {
        "collection_Id": collectionId,
        "collection_Title": collectionTitle,
        "collection_Description": collectionDescription,
        "login_user_Id": loginUserId,
        "cms_workspace_Id": cmsWorkspaceId,
        "is_active": isActive,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
    };
}
