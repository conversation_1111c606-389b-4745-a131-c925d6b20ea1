class HistoryResponse {
  final String status;
  final String msg;
  final HistoryData? data;

  HistoryResponse({
    required this.status,
    required this.msg,
    this.data,
  });

  factory HistoryResponse.fromJson(Map<String, dynamic> json) {
    return HistoryResponse(
      status: json['status'],
      msg: json['msg'],
      data: json['data'] != null ? HistoryData.fromJson(json['data']) : null,
    );
  }
}

class HistoryData {
  final int total;
  final int totalPages;
  final List<HistoryItem> history;

  HistoryData({
    required this.total,
    required this.totalPages,
    required this.history,
  });

  factory HistoryData.fromJson(Map<String, dynamic> json) {
    return HistoryData(
      total: json['total'] ?? 0,
      totalPages: json['totalPages'] ?? 0,
      history: json['history'] != null
          ? (json['history'] as List)
              .map((item) => HistoryItem.fromJson(item))
              .toList()
          : [],
    );
  }
}

class HistoryItem {
  final int apiRequestHistoryId;
  final int cmsWorkspaceId;
  final int loginUserId;
  final int apiRequestId;
  final Map<String, dynamic> apiRequestHistoryResponse;
  final int apiRequestHistoryStatusCode;
  final int apiRequestHistoryResponseTime;
  final int isActive;
  final int isDelete;
  final String createdAt;
  final String? updatedAt;
  final String? deletedAt;
  final String apiRequestName;
  final String apiRequestUrl;
  final String apiRequestMethod;
  final Map<String, dynamic> response;

  HistoryItem({
    required this.apiRequestHistoryId,
    required this.cmsWorkspaceId,
    required this.loginUserId,
    required this.apiRequestId,
    required this.apiRequestHistoryResponse,
    required this.apiRequestHistoryStatusCode,
    required this.apiRequestHistoryResponseTime,
    required this.isActive,
    required this.isDelete,
    required this.createdAt,
    this.updatedAt,
    this.deletedAt,
    required this.apiRequestName,
    required this.apiRequestUrl,
    required this.apiRequestMethod,
    required this.response,
  });

  factory HistoryItem.fromJson(Map<String, dynamic> json) {
    return HistoryItem(
      apiRequestHistoryId: json['api_request_history_Id'] ?? 0,
      cmsWorkspaceId: json['cms_workspace_Id'] ?? 0,
      loginUserId: json['login_user_Id'] ?? 0,
      apiRequestId: json['api_request_Id'] ?? 0,
      apiRequestHistoryResponse: json['api_request_history_Response'] is Map<String, dynamic>
          ? json['api_request_history_Response']
          : json['api_request_history_Response'] is Map
              ? Map<String, dynamic>.from(json['api_request_history_Response'])
              : {},
      apiRequestHistoryStatusCode: json['api_request_history_Status_Code'] ?? 0,
      apiRequestHistoryResponseTime: json['api_request_history_Response_Time'] ?? 0,
      isActive: json['is_active'] ?? 1,
      isDelete: json['is_delete'] ?? 0,
      createdAt: json['created_at'] ?? DateTime.now().toString(),
      updatedAt: json['updated_at'],
      deletedAt: json['deleted_at'],
      apiRequestName: json['api_request_Name'] ?? 'Unnamed Request',
      apiRequestUrl: json['api_request_Url'] ?? '',
      apiRequestMethod: json['api_request_Method'] ?? 'GET',
      response: json['response'] is Map<String, dynamic> ? json['response'] :
               json['response'] is Map ? Map<String, dynamic>.from(json['response']) : {},
    );
  }
}
