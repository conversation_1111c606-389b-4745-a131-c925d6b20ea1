import 'dart:convert';

class RequestDetailsResponse {
  final String status;
  final String message;
  final RequestDetailsModel? data;

  RequestDetailsResponse({
    required this.status,
    required this.message,
    this.data,
  });

  factory RequestDetailsResponse.fromJson(Map<String, dynamic> json) {
    return RequestDetailsResponse(
      status: json['status'] ?? '',
      message: json['message'] ?? '',
      data: json['data'] != null ? RequestDetailsModel.fromJson(json['data']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.toJson(),
    };
  }
}

class RequestDetailsModel {
  final int id;
  final String name;
  final String url;
  final String method;
  final Map<String, dynamic> headers;
  final BodyModel body;
  final Map<String, dynamic> params;
  final String? authType;
  final int? collectionId;
  final int? folderId;
  final int? order;
  final int? workspaceId;
  final int? userId;
  final int? isActive;
  final int? isDelete;
  final String? createdAt;
  final String? updatedAt;
  final LastResponseModel? lastResponse;

  RequestDetailsModel({
    required this.id,
    required this.name,
    required this.url,
    required this.method,
    required this.headers,
    required this.body,
    required this.params,
    this.authType,
    this.collectionId,
    this.folderId,
    this.order,
    this.workspaceId,
    this.userId,
    this.isActive,
    this.isDelete,
    this.createdAt,
    this.updatedAt,
    this.lastResponse,
  });

  factory RequestDetailsModel.fromJson(Map<String, dynamic> json) {
    return RequestDetailsModel(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      url: json['url'] ?? '',
      method: json['method'] ?? 'GET',
      headers: json['headers'] is Map ? Map<String, dynamic>.from(json['headers']) : {},
      body: json['body'] != null ? BodyModel.fromJson(json['body']) : BodyModel(type: 'none', content: '{}'),
      params: json['params'] is Map ? Map<String, dynamic>.from(json['params']) : {},
      authType: json['auth_type'],
      collectionId: json['collection_id'],
      folderId: json['folder_id'],
      order: json['order'],
      workspaceId: json['workspace_id'],
      userId: json['user_id'],
      isActive: json['is_active'],
      isDelete: json['is_delete'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
      lastResponse: json['lastResponse'] != null ? LastResponseModel.fromJson(json['lastResponse']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'url': url,
      'method': method,
      'headers': headers,
      'body': body.toJson(),
      'params': params,
      'auth_type': authType,
      'collection_id': collectionId,
      'folder_id': folderId,
      'order': order,
      'workspace_id': workspaceId,
      'user_id': userId,
      'is_active': isActive,
      'is_delete': isDelete,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'lastResponse': lastResponse?.toJson(),
    };
  }
}

class BodyModel {
  final String type;
  final String content;

  BodyModel({
    required this.type,
    required this.content,
  });

  factory BodyModel.fromJson(Map<String, dynamic> json) {
    return BodyModel(
      type: json['type'] ?? 'none',
      content: json['content'] is String ? json['content'] : jsonEncode(json['content'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'content': content,
    };
  }

  dynamic getContentAsJson() {
    try {
      return jsonDecode(content);
    } catch (e) {
      return {};
    }
  }
}

class LastResponseModel {
  final int id;
  final int requestId;
  final int statusCode;
  final Map<String, dynamic> headers;
  final Map<String, dynamic> body;
  final int responseTime;
  final String createdAt;
  final String? source;

  LastResponseModel({
    required this.id,
    required this.requestId,
    required this.statusCode,
    required this.headers,
    required this.body,
    required this.responseTime,
    required this.createdAt,
    this.source,
  });

  factory LastResponseModel.fromJson(Map<String, dynamic> json) {
    return LastResponseModel(
      id: json['id'] ?? 0,
      requestId: json['request_id'] ?? 0,
      statusCode: json['status_code'] ?? 0,
      headers: json['headers'] is Map ? Map<String, dynamic>.from(json['headers']) : {},
      body: json['body'] is Map ? Map<String, dynamic>.from(json['body']) : {},
      responseTime: json['response_time'] ?? 0,
      createdAt: json['created_at'] ?? '',
      source: json['source'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'request_id': requestId,
      'status_code': statusCode,
      'headers': headers,
      'body': body,
      'response_time': responseTime,
      'created_at': createdAt,
      'source': source,
    };
  }
}
