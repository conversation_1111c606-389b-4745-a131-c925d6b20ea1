import 'package:flutter/material.dart';

import '../globals.dart';
import 'color_config.dart';

TextStyle mRegularTextStyle16({
  Color textColor = Colors.white,
  var textSize = 16.0,
}) {
  return TextStyle(
    fontSize: textSize,
    color: textColor,
    fontFamily: 'inter',
    fontWeight: FontWeight.w400,
  );
}

TextStyle mMediumTextStyle16({
  Color textColor = Colors.white,
  var textSize = 16.0,
}) {
  return TextStyle(
    fontSize: textSize,
    color: textColor,
    fontFamily: 'inter',
    fontWeight: FontWeight.w500,
  );
}

TextStyle mSemiBoldTextStyle16({
  Color textColor = Colors.white,
  var textSize = 16.0,
}) {
  return TextStyle(
    fontSize: textSize,
    color: textColor,
    fontFamily: 'inter',
    fontWeight: FontWeight.w600,
  );
}

TextStyle mBoldTextStyle16({
  Color textColor = Colors.white,
  double? textSize = 16.0,
}) {
  return TextStyle(
    fontSize: textSize,
    color: textColor,
    fontFamily: 'inter',
    fontWeight: FontWeight.w700,
  );
}



//capsule decoration
BoxDecoration mContainerCapsuleFillableStyle({
  Color backgroundColor = AppThemeColor.transparent,
  double borderRadius = Globals.containerCapsuleCornerRadius,
}) {
  return BoxDecoration(
    color: backgroundColor,
    borderRadius: BorderRadius.circular(borderRadius),
  );
}

BoxDecoration mContainerCapsuleNonFillableStyle({
  Color backgroundColor = AppThemeColor.transparent,
  double borderRadius = Globals.containerCapsuleCornerRadius,
  double borderWidth = Globals.defaultTextInputBorderWidth,
  Color borderColor = AppThemeColor.midGrey,
  bool isBorder = false,
}) {
  return BoxDecoration(
    color: backgroundColor,
    borderRadius: BorderRadius.circular(borderRadius),
    border: Border.all(
      width: borderWidth,
      color: borderColor,
    ),
  );
}

ThemeData calenderPickerTheme(BuildContext context){
  return Theme.of(context).copyWith(
    colorScheme: const ColorScheme.light(
      primary: AppThemeColor.primaryColor,
      onPrimary: Colors.white, // header text color
      //onSurfaceVariant: Color(0xFF4A148C), //top header text color change [Date, {Select date} text color, pencil button]
      //onSurface: Color(0xFF880E4F), //whole calender date text color change
      //outlineVariant: Colors.yellowAccent, //change the color of the underline under the date text
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: AppThemeColor.red, // button text color
      ),
    ),
  );
}

ThemeData timePickerTheme(BuildContext context){
  return Theme.of(context).copyWith(
    colorScheme: const ColorScheme.light(
      primary: AppThemeColor.primaryColor, //Color selected circle background color
      primaryContainer:Color(0xFFFED6E0), //hour minute block selected background color
      onPrimaryContainer:AppThemeColor.lightBlack, //hour minute selected block text color

      // secondary: AppThemeColor.red,//Color(0xFFFED6E0), //AM/PM block selected background color
      // onSecondary: AppThemeColor.white, //AM/PM block selected text color

       tertiaryContainer:AppThemeColor.red,  //Overwride secondary color (AM/PM Block selected background)
       onTertiaryContainer: AppThemeColor.white, //Overwrite onSecondary color (AM/PM selected text color)

      //background:AppThemeColor.green,
      onBackground:AppThemeColor.grey, //AM/PM border color change
      // surface: Color(0xFFFFFFFF),
      // onSurface: Color(0xFFFFFF07), //Whole time picker clock text & layout non selected text color change [if use onSurfaceVariant then Leave out text color AM/PM]
      // surfaceVariant:Color(0xFF00B8D4), //Clock background & Non Selected time [Hour, Minute] block
      // onSurfaceVariant:Color(0xFF9CCC65), //AM/PM block non selected text color
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: AppThemeColor.red, // button text color
      ),
    ),
  );
}
