import 'dart:ui';

import 'package:flutter/material.dart';

class AppThemeColor {
  // Define your color scheme for light mode
  static const Color primaryColor = Color(0xffff0a38);

  //define common color
  static const Color transparent = Colors.transparent;
  static const Color white = Colors.white;

  static const Color lightGrey = Color(0xfff4f4f4);
  static const Color midGrey = Color(0xffe0e0e0);
  static const Color grey = Color(0xff777777);
  static const Color darkGrey50 = Color(0x806c6969);
  static const Color darkGrey = Color(0xff373636);
  static const Color greyText = Color(0xff6B7280);
  static const Color greyButton = Color(0xffD2D2D2);
  static const Color greyDisableButton = Color(0xffe1e1e1);

  static const Color lightBlack = Color(0xff1c1b1f);
  static const Color black = Colors.black;
  static const Color extraLightBlack = Color(0x1f000000);

  static const Color lightRed = Color(0xfffff2f2);
  static const Color red = Color(0xffff0a38);
  static const Color darkRed = Color(0xffe7123a);

  static const Color extraLightGreen = Color(0xfff8fffa); //this color use for only notification card background
  static const Color lightGreen = Color(0xffc1fad4);
  static const Color green = Color(0xff3abf48);
  static const Color buttonGreen = Color(0xff4AC857);

  static const Color darkBlue = Color(0xff05178E);
  static const Color lightBlue = Color(0xff66B8E3);
  static const Color backButtonBlue = Color(0xff007AFF);

  //for this project
  static const Color hintTextColor2 = Color(0xff949494);
  static const Color hintTextColor = Color(0xff515151);
  static const Color textFieldBorderActiveColor = Color(0xffC8C8C8);
  static const Color lightBackground = Color(0xff363636);
  static const Color commonBackground = Color(0xFF272623);
  static const Color crossButtonBackground = Color(0xff272623);
  static const Color dialogDescriptionTextColor = Color(0xff8E979F);
  static const Color buttonBlue = Color(0xff007AFF);
  static const Color dropDownChildTextColor = Color(0xffB6B6B6);
  static const Color tabbarBgColor = Color(0xff52514E);
  static const Color tabbarTextColor = Color(0xffC9C9C9);
  static const Color tabbarUnderLineColor = Color(0xff1874DA);
  static const Color textButtonBlue = Color(0xff1874DA);
  static const Color commonBorderColor = Color(0xff8F8F8F);
  static const Color commonDropdownArrowColor = Color(0xff5A5956);
  static const Color tabUnselectedTextColor = Color(0xffB3B1AB);
  static const Color tableHeaderTextColor = Color(0xffD8DBE2);
  static const Color tableTextColor = Color(0xff808080);
  static const Color dividerBackgroundColor = Color(0xff666666);
  static const Color infoIconColor = Color(0xffD8DBE2);
  static const Color checkBoxFillColor = Color(0xff3E3D39);
  static const Color checkBoxTextColor = Color(0xff7C7B84);
  static const Color hoverColor = Color(0xff5b5b5b);
  static const Color selectedMenuColor = Color(0xff4F575E);





  static const Color bottomSheetSwipeColor = Color(0xffADB7CF);
  static const Color bottomBorderDay = Color(0xFFE5EAF5);
  static const Color bottomBorderNight = Color(0xFF504F4F);
  static const Color greyDark = Color(0xFFFAFAFA);
  static const Color bottomSheetBoxShadowColorDay = Color(0x272c4141);
  static const Color bottomSheetBoxShadowColorNight = Color(0x7FB2B7C7);
  static const Color textInputLight = Color(0xffcfd5de);
  static const Color redBox = Color(0xFFFFC7CF);
  static const Color textInputDark = lightGrey;
  static const Color textInputPrefixSuffix = midGrey;
  static const Color textInputActiveColor = midGrey;
  static const Color textInputDisabledColor = midGrey;
  static const Color textInputErrorColor = red;
  static const Color textInputHintLightColor = textInputLight;
  static const Color textInputHintDarkColor = midGrey;

  static const Color tabSelectedColor = red;
  static const Color tabUnSelectedColor = grey;
  static const Color tabIndicatorColor = red;
  static const Color tabLabelColor = red;
  static const Color tabIndicatorBackgroundColor = midGrey;

  ///DAY NIGHT COLOR
  static Color backgroundColorBlackWhite(BuildContext context) {
    return isDarkMode(context) ? lightBlack : white;
  }

  static Color backgroundTextInputBox(BuildContext context) {
    return isDarkMode(context) ? textInputDark : textInputLight;
  }

  static Color backgroundContainerColorGreyLightGrey(BuildContext context) {
    return isDarkMode(context) ? darkGrey50 : lightGrey;
  }

  static Color badgeColorGreenRed(BuildContext context) {
    return isDarkMode(context) ? red : green;
  }

  static Color cardColorExtraLightGreenTransparent(BuildContext context) {
    return isDarkMode(context) ? transparent : extraLightGreen;
  }

  static Color iconColorRedGrey(BuildContext context) {
    return isDarkMode(context) ? midGrey : red;
  }

  static Color textColorBlackMidGrey(BuildContext context) {
    return isDarkMode(context) ? white : white;
  }

  static Color textColorBlackGrey(BuildContext context) {
    return isDarkMode(context) ? grey : lightBlack;
  }

  static Color textColorGreyLightGrey(BuildContext context) {
    return isDarkMode(context) ? midGrey : grey;
  }

  static Color textColorRedGrey(BuildContext context) {
    return isDarkMode(context) ? midGrey : midGrey;
  }

  static Color textColorDarkBlueLightBlue(BuildContext context) {
    return isDarkMode(context) ? lightBlue : darkBlue;
  }

  static Color toolbarTextColorWhiteGray(BuildContext context) {
    return isDarkMode(context) ? midGrey : lightBlack;
  }

  static Color textInputHintColorGrayMidGrey(BuildContext context) {
    return isDarkMode(context) ? textInputHintDarkColor : textInputHintLightColor;
  }

  static Color toolbarColorWhiteGray(BuildContext context) {
    return isDarkMode(context) ? darkGrey : white;
  }

  static Color toolbarBackIconColor(BuildContext context) {
    return isDarkMode(context) ? midGrey : lightBlack;
  }

  static Color bottomTabButtonSelected(BuildContext context) {
    return isDarkMode(context) ? red : red;
  }

  static Color outlineButtonSideColor(BuildContext context) {
    return isDarkMode(context) ? midGrey : red;
  }

  static Color imageCircleBorder(BuildContext context) {
    return isDarkMode(context) ? midGrey : midGrey;
  }

  static Color profileImageBackground(BuildContext context) {
    return isDarkMode(context) ? const Color(0xffa4a4a4) : const Color(0xffa4a4a4);
  }

  static Color bottomTabButtonUnSelected(BuildContext context) {
    return isDarkMode(context) ? midGrey : grey;
  }

  static Color myLocationColorWhiteBlack(BuildContext context) {
    return isDarkMode(context) ? white : lightBlack;
  }

  static Color bottomSheetBottomBorderColor(BuildContext context) {
    return isDarkMode(context) ? bottomBorderNight : bottomBorderDay;
  }

  static Color topSheetBottomBorderColor(BuildContext context) {
    return isDarkMode(context) ? transparent : bottomBorderDay;
  }

  static Color statusTextColorGreenGrey(BuildContext context) {
    return isDarkMode(context) ? grey : green;
  }

  static Color cardBackgroundColor(BuildContext context) {
    return isDarkMode(context) ? black : greyDark;
  }

  static Color capsuleBoxColor(BuildContext context) {
    return isDarkMode(context) ? lightGrey : midGrey;
  }

  static Color assignedBox(BuildContext context) {
    return redBox;
  }

  static Color completedBox(BuildContext context) {
    return lightGreen;
  }

  static Color switchColor(BuildContext context) {
    return isDarkMode(context) ? Colors.grey : midGrey;
  }
  static Color switchThumbColor(BuildContext context) {
    return isDarkMode(context) ? darkGrey : const Color(0xfff5f5f5);
  }

  static Color boxShadowColor(BuildContext context) {
    return isDarkMode(context) ? bottomSheetBoxShadowColorNight : bottomSheetBoxShadowColorDay;
  }

  static bool isDarkMode(BuildContext context) {
    var brightness = MediaQuery.of(context).platformBrightness;
    return brightness == Brightness.dark;
  }
}
