bool isCheckEmpty(mixedVar) {
  if (mixedVar is List || mixedVar is Map) {
    if (mixedVar.isEmpty) {
      return true;
    }
  } else {
    //print('checkEmpty in 2');
    var undef;
    var undefined;
    var i;
    var emptyValues = [undef, null, 'null', 'Null', 'NULL','none', false, 0, '', '0', '0.00', '0.0', 'empty', undefined, 'undefined'];
    var len = emptyValues.length;
    if (mixedVar is String) {
      mixedVar = mixedVar.trim();
    }

    for (i = 0; i < len; i++) {
      if (mixedVar == emptyValues[i]) {
        return true;
      }
    }
  }
  return false;
}

bool checkEmpty<T>(List<T>? list) {
    return list == null || list.isEmpty;
}