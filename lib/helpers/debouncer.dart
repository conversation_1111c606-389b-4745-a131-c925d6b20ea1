import 'dart:async';
import 'package:flutter/foundation.dart';

/// A class that implements debouncing functionality.
/// Debouncing ensures that a function is not called again until a certain amount of time has passed.
class Debouncer {
  final int milliseconds;
  Timer? _timer;

  Debouncer({required this.milliseconds});

  /// Run the provided callback after the debounce period.
  /// If run is called again before the debounce period ends, the timer is reset.
  void run(VoidCallback action) {
    _timer?.cancel();
    _timer = Timer(Duration(milliseconds: milliseconds), action);
  }

  /// Cancel the timer if it's active.
  void cancel() {
    _timer?.cancel();
  }
}
