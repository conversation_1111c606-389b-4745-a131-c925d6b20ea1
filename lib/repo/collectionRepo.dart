import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:postman_flutter/api_constant.dart';
import 'package:postman_flutter/model/collection_detail_model.dart';
import 'package:postman_flutter/model/collection_model.dart';


class CollectionRepository {
  Future<CollectionResponse> fetchCollections(String workspaceId) async {
    workspaceId = ApiConstant.cmsWorkspaceId;
    final params = {'cms_workspace_Id': workspaceId};
    final url = Uri.parse(ApiConstant.getCollectionsList);
    final response = await http.post(
      url,
      headers: ApiHeaders.headers,
      body: jsonEncode(params),
    );
    if (response.statusCode == 200) {
      final jsonResponse = jsonDecode(response.body);
      return CollectionResponse.fromJson(jsonResponse);
    } else {
      throw Exception('Failed to load collections');
    }
  }

  Future<CollectionDetailResponse> fetchCollectionDetails(int collectionId, int workspaceId) async {
    final url = Uri.parse('${ApiConstant.getCollectionDetails}$collectionId');

    try {
      final response = await http.get(
        url,
        headers: ApiHeaders.headers,
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return CollectionDetailResponse.fromJson(jsonResponse);
      } else {
        return CollectionDetailResponse(
          status: 'Error',
          msg: 'Failed to load collection details. Status code: ${response.statusCode}',
        );
      }
    } catch (e) {
      return CollectionDetailResponse(
        status: 'Error',
        msg: 'Exception occurred: $e',
      );
    }
  }

  Future<CollectionResponse> createCollection({
    required String title,
    String description = '',
    required int workspaceId,
  }) async {
    final url = Uri.parse(ApiConstant.createCollection);
    final params = {
      'collection_Title': title,
      'collection_Description': description,
      'cms_workspace_Id': workspaceId,
    };

    try {
      final response = await http.post(
        url,
        headers: ApiHeaders.headers,
        body: jsonEncode(params),
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return CollectionResponse.fromJson(jsonResponse);
      } else {
        return CollectionResponse(
          status: 'Error',
          msg: 'Failed to create collection. Status code: ${response.statusCode}',
        );
      }
    } catch (e) {
      return CollectionResponse(
        status: 'Error',
        msg: 'Exception occurred: $e',
      );
    }
  }

  Future<CollectionResponse> deleteCollection(int collectionId) async {
    final url = Uri.parse(ApiConstant.deleteCollection);
    final params = {
      'collection_Id': collectionId.toString(),
    };

    try {
      final response = await http.post(
        url,
        headers: ApiHeaders.headers,
        body: jsonEncode(params),
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return CollectionResponse.fromJson(jsonResponse);
      } else {
        return CollectionResponse(
          status: 'Error',
          msg: 'Failed to delete collection. Status code: ${response.statusCode}',
        );
      }
    } catch (e) {
      return CollectionResponse(
        status: 'Error',
        msg: 'Exception occurred: $e',
      );
    }
  }

  Future<CollectionResponse> updateCollection({
    required int collectionId,
    required String title,
    String description = '',
    required int workspaceId,
  }) async {
    final url = Uri.parse('${ApiConstant.updateCollection}$collectionId');
    final params = {
      'collection_Title': title,
      'collection_Description': description,
      'cms_workspace_Id': workspaceId,
    };

    try {
      final response = await http.post(
        url,
        headers: ApiHeaders.headers,
        body: jsonEncode(params),
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return CollectionResponse.fromJson(jsonResponse);
      } else {
        return CollectionResponse(
          status: 'Error',
          msg: 'Failed to update collection. Status code: ${response.statusCode}',
        );
      }
    } catch (e) {
      return CollectionResponse(
        status: 'Error',
        msg: 'Exception occurred: $e',
      );
    }
  }
}
