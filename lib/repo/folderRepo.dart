import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:postman_flutter/api_constant.dart';
import 'package:postman_flutter/model/folder_model.dart';

class FolderRepository {
  Future<FolderResponse> createFolder({
    required String name,
    required String description,
    String? collectionId,
    String? parentFolderId,
  }) async {
    final url = Uri.parse(ApiConstant.createFolder);
    final Map<String, dynamic> params = {
      'name': name,
      'description': description,
    };
    print('collectionId: $collectionId, parentFolderId: $parentFolderId');
    if (collectionId != null) {
      params['collection_id'] = collectionId;
    } else if (parentFolderId != null) {
      params['parent_folder_id'] = parentFolderId;
    }

    try {
      final response = await http.post(
        url,
        headers: ApiHeaders.headers,
        body: jsonEncode(params),
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return FolderResponse.fromJson(jsonResponse);
      } else {
        return FolderResponse(
          status: 'Error',
          msg: 'Failed to create folder. Status code: ${response.statusCode}',
        );
      }
    } catch (e) {
      return FolderResponse(
        status: 'Error',
        msg: 'Exception occurred: $e',
      );
    }
  }

  Future<FolderResponse> updateFolder({
    required int folderId,
    required String name,
    required String description,
    required String collectionId,
    String? parentFolderId,
  }) async {
    final url = Uri.parse('${ApiConstant.updateFolder}$folderId');
    final params = {
      'name': name,
      'description': description,
      'collection_id': collectionId,
    };


    if (parentFolderId != null) {
      params['parent_folder_id'] = parentFolderId;
    }

    try {
      final response = await http.post(
        url,
        headers: ApiHeaders.headers,
        body: jsonEncode(params),
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return FolderResponse.fromJson(jsonResponse);
      } else {
        return FolderResponse(
          status: 'Error',
          msg: 'Failed to update folder. Status code: ${response.statusCode}',
        );
      }
    } catch (e) {
      return FolderResponse(
        status: 'Error',
        msg: 'Exception occurred: $e',
      );
    }
  }

  Future<FolderResponse> deleteFolder(int folderId) async {
    final url = Uri.parse(ApiConstant.deleteFolder);
    final params = {
      'folder_id': folderId.toString(),
    };

    try {
      final response = await http.post(
        url,
        headers: ApiHeaders.headers,
        body: jsonEncode(params),
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return FolderResponse.fromJson(jsonResponse);
      } else {
        return FolderResponse(
          status: 'Error',
          msg: 'Failed to delete folder. Status code: ${response.statusCode}',
        );
      }
    } catch (e) {
      return FolderResponse(
        status: 'Error',
        msg: 'Exception occurred: $e',
      );
    }
  }
}
