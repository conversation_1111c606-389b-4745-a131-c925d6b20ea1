import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:postman_flutter/api_constant.dart';
import 'package:postman_flutter/model/history_model.dart';

class HistoryRepository {
  Future<HistoryResponse> getHistory({
    required int userId,
    required int workspaceId,
    required int limit,
    required int page,
  }) async {
    final apiUrl = Uri.parse(ApiConstant.getHistory);

    final params = {
      'user_id': userId,
      'workspace_id': workspaceId,
      'limit': limit,
      'page': page,
    };

    try {
      debugPrint('Fetching history with params: $params');
      final response = await http.post(
        apiUrl,
        headers: ApiHeaders.headers,
        body: jsonEncode(params),
      ).timeout(const Duration(seconds: 30));

      debugPrint('Get history response: ${response.body}');

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return HistoryResponse.fromJson(jsonResponse);
      } else {
        return HistoryResponse(
          status: 'error',
          msg: 'Failed to fetch history. Status code: ${response.statusCode}',
        );
      }
    } catch (e) {
      debugPrint('Error fetching history: $e');
      return HistoryResponse(
        status: 'error',
        msg: 'Error fetching history: $e',
      );
    }
  }

  Future<Map<String, dynamic>> clearHistory({
    required int userId,
    required int workspaceId,
  }) async {
    final apiUrl = Uri.parse(ApiConstant.clearHistory);

    final params = {
      'user_id': userId,
      'workspace_id': workspaceId,
    };

    try {
      debugPrint('Clearing history with params: $params');
      final response = await http.post(
        apiUrl,
        headers: ApiHeaders.headers,
        body: jsonEncode(params),
      ).timeout(const Duration(seconds: 30));

      debugPrint('Clear history response: ${response.body}');

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return jsonResponse;
      } else {
        return {
          'status': 'error',
          'msg': 'Failed to clear history. Status code: ${response.statusCode}',
        };
      }
    } catch (e) {
      debugPrint('Error clearing history: $e');
      return {
        'status': 'error',
        'msg': 'Error clearing history: $e',
      };
    }
  }
}
