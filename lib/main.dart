import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:path_provider/path_provider.dart';
import 'package:postman_flutter/features/home/<USER>/collection/collection_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/folder/folder_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/history/history_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/query_params/bloc/query_params_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/query_params/bloc/query_params_event.dart';
import 'package:postman_flutter/repo/collectionRepo.dart';
import 'package:postman_flutter/repo/folderRepo.dart';
import 'package:postman_flutter/repo/history_repo.dart';
import 'package:postman_flutter/repo/postmanRequestRepo.dart';
import 'package:postman_flutter/features/home/<USER>/postmanrequest/postman_request_bloc.dart';
import 'package:provider/provider.dart';
import 'features/common_widgets/common_snackbar.dart';
import 'features/common_widgets/dynamic_radio_tabs/bloc/tab_bloc.dart';
import 'features/home/<USER>/home_bloc.dart';
import 'features/home/<USER>/home_event.dart';
import 'features/home/<USER>/api_collection.dart';
import 'features/home/<USER>/hive_db_provider.dart';
import 'features/home/<USER>';
import 'features/home/<USER>/authorization/basic_auth/basic_auth.dart';
import 'features/home/<USER>/authorization/bloc/auth_subviews_bloc.dart';
import 'features/home/<USER>/authorization/bloc/authorization_bloc.dart';
import 'features/home/<USER>/body/form_data/bloc/form_data_bloc.dart';
import 'features/home/<USER>/body/form_data/bloc/form_data_event.dart';
import 'features/home/<USER>/body/json_data/bloc/json_data_bloc.dart';
import 'features/home/<USER>/header/bloc/headers_bloc.dart';
import 'features/home/<USER>/header/bloc/headers_event.dart';
import 'features/home/<USER>/send_request/request_bloc.dart';
import 'package:hive/hive.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'helpers/color_config.dart';



void main() async {

  WidgetsFlutterBinding.ensureInitialized();
  // Initialize Hive
  await Hive.initFlutter();

  await Hive.openBox('apiCollectionBox');
  //await HiveDBService().openRequestHistoryBox();
  await Hive.openBox('requestHistoryBox');

  runApp(
    MultiProvider(
      providers: [
        Provider<HiveDBService>(create: (_) => HiveDBService()),
        //Provider<CollectionDetailRepository>(create: (_) => CollectionDetailRepository()),
      ],
      child: MultiBlocProvider(
        providers: [
          BlocProvider(create: (_) => CollectionBloc(repository: CollectionRepository())),
          BlocProvider(create: (_) => FolderBloc(repository: FolderRepository())),
          BlocProvider(create: (_) => HomeBloc()..add(LoadApiDataEvent())),
          BlocProvider(create: (_) => HeadersBloc()),
          BlocProvider(create: (_) => FormDataBloc()..add(AddFormDataRowEvent())),
          BlocProvider(create: (_) => QueryParamsBloc()..add(AddQueryParamsRowEvent())),
          BlocProvider(create: (_) => JsonDataBloc()),
          BlocProvider(create: (_) => RequestBloc()),
          BlocProvider(create: (_) => PostmanRequestBloc(repository: PostmanRequestRepository())),
          BlocProvider(create: (_) => HistoryBloc(repository: HistoryRepository())),
          BlocProvider(create: (_) => AuthSubViewsBloc()),
          BlocProvider<TabBloc>(create: (_) => TabBloc()),
        ],
        child: MyApp(),
      ),
    ),
  );
  SnackbarService().scaffoldMessengerKey = GlobalKey<ScaffoldMessengerState>();
}



class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    print("::build");
    return MaterialApp(
      title: 'Kuick Postman',
      scaffoldMessengerKey: SnackbarService().scaffoldMessengerKey,
      debugShowCheckedModeBanner: false,
        theme: ThemeData(
          primaryColor: AppThemeColor.buttonBlue,
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.black),
          useMaterial3: true,
          progressIndicatorTheme: ProgressIndicatorThemeData(
            color: AppThemeColor.buttonBlue, // Set your desired default color
          ),
          snackBarTheme: SnackBarThemeData(
            backgroundColor: AppThemeColor.buttonBlue, // Set your desired color
          ),
          textSelectionTheme: TextSelectionThemeData(
            cursorColor: Colors.white,
            selectionColor: Colors.blue,
            selectionHandleColor: Colors.black,
          ),
        ),
      home:  HomePage(),
    );
  }
}
